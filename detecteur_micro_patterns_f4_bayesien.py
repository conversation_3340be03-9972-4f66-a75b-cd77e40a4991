#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DÉTECTEUR DE MICRO-PATTERNS BACCARAT LUPASCO
============================================
Métriques Ultra-Puissantes :
1. Dimension Fractale par Embedding F4 (Complexité géométrique cachée)
2. Prédictibilité Bayésienne (Information prédictive micro-patterns)

Auteur : Système d'analyse avancée
Date : 2025-07-03
"""

import json
import numpy as np
import time
import gc
from collections import Counter, defaultdict
from scipy import stats
from scipy.spatial.distance import pdist, squareform
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics import mutual_info_score
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class DetecteurMicroPatternsF4Bayesien:
    """Détecteur de micro-patterns avec métriques F4 et Bayésiennes"""
    
    def __init__(self):
        self.sequences_index3 = []
        self.sequences_index5 = []
        self.sequences_contexte = []  # (index1, index2, index3)
        self.resultats_f4 = {}
        self.resultats_bayesien = {}
        self.nb_parties = 0
        self.nb_mains_analysees = 0
        
    def charger_donnees(self, filename: str) -> bool:
        """Charge les données du fichier JSON condensé"""
        
        print(f"🔄 Chargement : {filename}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            parties = data.get('parties_condensees', [])
            self.nb_parties = len(parties)
            
            print(f"✅ Parties trouvées : {self.nb_parties:,}")
            
            # Extraire les séquences pour analyse
            for partie in parties:
                mains = partie.get('mains_condensees', [])
                
                # Extraire séquences INDEX3 et INDEX5 (skip dummy main)
                seq_index3 = []
                seq_index5 = []
                seq_contexte = []
                
                for main in mains[1:]:  # Skip première main (dummy)
                    if main.get('index3') and main.get('index5'):
                        seq_index3.append(main['index3'])
                        seq_index5.append(main['index5'])
                        
                        # Contexte complet pour analyse bayésienne
                        contexte = (
                            main.get('index1'),
                            main.get('index2'),
                            main.get('index3')
                        )
                        seq_contexte.append(contexte)
                
                if len(seq_index3) >= 10:  # Minimum pour analyse fractale
                    self.sequences_index3.append(seq_index3)
                    self.sequences_index5.append(seq_index5)
                    self.sequences_contexte.append(seq_contexte)
                    self.nb_mains_analysees += len(seq_index3)
            
            print(f"✅ Séquences valides : {len(self.sequences_index3):,}")
            print(f"✅ Mains analysées : {self.nb_mains_analysees:,}")
            
            return len(self.sequences_index3) > 0
            
        except Exception as e:
            print(f"❌ Erreur chargement : {e}")
            return False
    
    def estimer_lag_optimal(self, serie: np.ndarray, max_lag: int = 15) -> int:
        """Estime le lag optimal par information mutuelle (Système F4)"""
        
        if len(serie) < max_lag * 2:
            return min(3, len(serie) // 4)
        
        mutual_infos = []
        
        for lag in range(1, max_lag + 1):
            if len(serie) > lag + 5:
                x = serie[:-lag]
                y = serie[lag:]
                
                # Discrétiser pour mutual_info_score
                x_discrete = np.digitize(x, bins=np.linspace(x.min(), x.max(), 10))
                y_discrete = np.digitize(y, bins=np.linspace(y.min(), y.max(), 10))
                
                try:
                    mi = mutual_info_score(x_discrete, y_discrete)
                    mutual_infos.append(mi)
                except:
                    mutual_infos.append(0)
        
        if not mutual_infos:
            return 3
        
        # Premier minimum local = lag optimal
        for i in range(1, len(mutual_infos) - 1):
            if (mutual_infos[i] < mutual_infos[i-1] and 
                mutual_infos[i] < mutual_infos[i+1] and 
                mutual_infos[i] > 0):
                return i + 1
        
        # Si pas de minimum, prendre le lag avec MI maximale
        return np.argmax(mutual_infos) + 1 if mutual_infos else 3
    
    def box_counting_multidimensionnel(self, embedded_points: np.ndarray) -> float:
        """Calcule dimension fractale par box-counting CORRIGÉ dans l'espace d'embedding"""

        if len(embedded_points) < 15:
            return 1.0

        # Vérifier variabilité des données
        if np.std(embedded_points.flatten()) < 1e-6:
            return 1.0  # Données trop uniformes

        # Normaliser les points dans [0,1]^d avec marge
        points_norm = embedded_points.copy().astype(float)
        for dim in range(points_norm.shape[1]):
            col = points_norm[:, dim]
            col_min, col_max = col.min(), col.max()
            if col_max > col_min:
                points_norm[:, dim] = (col - col_min) / (col_max - col_min)
            else:
                points_norm[:, dim] = 0.5  # Valeur constante

        # Ajouter petit bruit pour éviter dégénérescence
        points_norm += np.random.normal(0, 1e-6, points_norm.shape)
        points_norm = np.clip(points_norm, 0, 1)

        # Tailles de boîtes plus adaptées
        box_sizes = np.array([0.5, 0.25, 0.125, 0.0625, 0.03125, 0.015625])
        box_counts = []

        for box_size in box_sizes:
            # Grille de boîtes
            n_boxes_per_dim = max(2, int(1.0 / box_size))

            # Convertir points en indices de boîtes
            box_indices = np.floor(points_norm * n_boxes_per_dim).astype(int)
            box_indices = np.clip(box_indices, 0, n_boxes_per_dim - 1)

            # Compter boîtes uniques occupées
            unique_boxes = set()
            for row in box_indices:
                unique_boxes.add(tuple(row))

            box_counts.append(len(unique_boxes))

        # Filtrer les valeurs valides
        valid_indices = [i for i, count in enumerate(box_counts) if count > 0]
        if len(valid_indices) < 3:
            return 1.0

        valid_sizes = [box_sizes[i] for i in valid_indices]
        valid_counts = [box_counts[i] for i in valid_indices]

        # Régression linéaire log(N) vs log(1/r)
        log_inv_sizes = np.log(1.0 / np.array(valid_sizes))
        log_counts = np.log(valid_counts)

        if len(log_inv_sizes) >= 3 and np.std(log_inv_sizes) > 1e-10:
            try:
                slope, intercept, r_value, p_value, std_err = stats.linregress(log_inv_sizes, log_counts)

                # Dimension fractale = pente
                if abs(r_value) > 0.5 and not np.isnan(slope):  # Corrélation acceptable
                    dimension = max(0.5, min(slope, embedded_points.shape[1] * 1.2))
                    return dimension
            except:
                pass

        # Estimation alternative par corrélation
        return self._estimer_dimension_correlation(embedded_points)

    def _estimer_dimension_correlation(self, points: np.ndarray) -> float:
        """Estimation alternative de dimension par analyse de corrélation"""

        if len(points) < 10:
            return 1.0

        # Calculer matrice de distances
        try:
            distances = pdist(points)
            if len(distances) == 0:
                return 1.0

            # Analyser distribution des distances
            dist_mean = np.mean(distances)
            dist_std = np.std(distances)

            if dist_std > 1e-6:
                # Dimension basée sur la dispersion relative
                cv = dist_std / dist_mean  # Coefficient de variation
                dimension = 1.0 + cv * points.shape[1] * 0.5
                return min(dimension, points.shape[1] + 0.5)

        except:
            pass

        return 1.5  # Valeur par défaut plus réaliste
    
    def analyser_dimension_fractale_f4(self):
        """Analyse F4 : Dimension fractale par embedding de coordonnées retardées"""
        
        print("\n🔬 ANALYSE F4 : DIMENSION FRACTALE PAR EMBEDDING")
        print("=" * 60)
        
        # Convertir INDEX3 en valeurs numériques
        mapping = {'BANKER': 0, 'PLAYER': 1, 'TIE': 2}
        
        dimensions_par_embedding = defaultdict(list)
        lags_optimaux = []
        
        # Échantillonnage intelligent pour performance
        nb_sequences = len(self.sequences_index3)
        if nb_sequences > 2000:
            step = max(1, nb_sequences // 2000)
            sequences_echantillon = self.sequences_index3[::step][:2000]
            print(f"📊 Échantillonnage : {len(sequences_echantillon):,} séquences (1/{step})")
        else:
            sequences_echantillon = self.sequences_index3
            print(f"📊 Analyse complète : {len(sequences_echantillon):,} séquences")
        
        for i, sequence in enumerate(sequences_echantillon):
            if len(sequence) >= 20:  # Minimum pour embedding
                # Convertir en série numérique
                serie = np.array([mapping.get(x, 0) for x in sequence])
                
                # Estimer lag optimal
                lag_optimal = self.estimer_lag_optimal(serie)
                lags_optimaux.append(lag_optimal)
                
                # Tester différentes dimensions d'embedding
                for dim_embed in range(2, 8):  # Dimensions 2 à 7
                    if len(serie) >= (dim_embed - 1) * lag_optimal + 10:
                        # Construire vecteurs d'embedding
                        embedded = []
                        for j in range(len(serie) - (dim_embed - 1) * lag_optimal):
                            vecteur = [serie[j + k * lag_optimal] for k in range(dim_embed)]
                            embedded.append(vecteur)
                        
                        embedded = np.array(embedded)
                        
                        # Calculer dimension fractale
                        dim_fractale = self.box_counting_multidimensionnel(embedded)
                        
                        if dim_fractale > 0:
                            dimensions_par_embedding[dim_embed].append(dim_fractale)
            
            # Progression
            if (i + 1) % 200 == 0:
                print(f"     Séquences F4 analysées : {i+1:,}/{len(sequences_echantillon):,}")
                gc.collect()
        
        # Synthèse des résultats F4
        print("\n✅ RÉSULTATS F4 - DIMENSIONS FRACTALES PAR EMBEDDING :")
        
        for dim_embed in sorted(dimensions_par_embedding.keys()):
            dims = np.array(dimensions_par_embedding[dim_embed])
            if len(dims) > 0:
                moyenne = np.mean(dims)
                std = np.std(dims)
                mediane = np.median(dims)
                
                print(f"  Embedding {dim_embed}D : {moyenne:.3f} ± {std:.3f} "
                      f"(médiane: {mediane:.3f}, {len(dims):,} échantillons)")
                
                self.resultats_f4[f'embedding_{dim_embed}d'] = {
                    'moyenne': moyenne,
                    'std': std,
                    'mediane': mediane,
                    'count': len(dims),
                    'dimensions': dims.tolist()
                }
        
        # Statistiques des lags optimaux
        if lags_optimaux:
            lag_moyen = np.mean(lags_optimaux)
            print(f"\n📊 Lag optimal moyen : {lag_moyen:.1f} (range: {min(lags_optimaux)}-{max(lags_optimaux)})")
            self.resultats_f4['lag_optimal'] = {
                'moyenne': lag_moyen,
                'min': min(lags_optimaux),
                'max': max(lags_optimaux),
                'distribution': Counter(lags_optimaux)
            }

    def analyser_predictibilite_bayesienne(self):
        """Analyse Bayésienne : Prédictibilité des micro-patterns"""

        print("\n🧠 ANALYSE BAYÉSIENNE : PRÉDICTIBILITÉ MICRO-PATTERNS")
        print("=" * 60)

        # Modèles bayésiens à différents niveaux de contexte
        modeles = {
            'index2_vers_index3': defaultdict(lambda: defaultdict(int)),
            'index1_index2_vers_index3': defaultdict(lambda: defaultdict(int)),
            'index5_precedent_vers_index3': defaultdict(lambda: defaultdict(int)),
            'sequence_2_vers_index3': defaultdict(lambda: defaultdict(int)),
            'sequence_3_vers_index3': defaultdict(lambda: defaultdict(int))
        }

        # Construire les modèles à partir des données
        for seq_contexte in self.sequences_contexte:
            for i, (index1, index2, index3) in enumerate(seq_contexte):
                if all([index1 is not None, index2, index3]):

                    # Modèle 1: INDEX2 → INDEX3
                    modeles['index2_vers_index3'][index2][index3] += 1

                    # Modèle 2: (INDEX1, INDEX2) → INDEX3
                    contexte = (index1, index2)
                    modeles['index1_index2_vers_index3'][contexte][index3] += 1

                    # Modèle 3: INDEX5 précédent → INDEX3
                    if i > 0:
                        index5_prec = f"{seq_contexte[i-1][0]}_{seq_contexte[i-1][1]}_{seq_contexte[i-1][2]}"
                        modeles['index5_precedent_vers_index3'][index5_prec][index3] += 1

                    # Modèle 4: Séquence de 2 INDEX3 → INDEX3 suivant
                    if i > 0:
                        seq_2 = (seq_contexte[i-1][2], index3)
                        if i < len(seq_contexte) - 1:
                            index3_suivant = seq_contexte[i+1][2]
                            modeles['sequence_2_vers_index3'][seq_2][index3_suivant] += 1

                    # Modèle 5: Séquence de 3 INDEX3 → INDEX3 suivant
                    if i > 1:
                        seq_3 = (seq_contexte[i-2][2], seq_contexte[i-1][2], index3)
                        if i < len(seq_contexte) - 1:
                            index3_suivant = seq_contexte[i+1][2]
                            modeles['sequence_3_vers_index3'][seq_3][index3_suivant] += 1

        # Calculer prédictibilité pour chaque modèle
        print("✅ PRÉDICTIBILITÉ BAYÉSIENNE PAR MODÈLE :")

        for nom_modele, modele in modeles.items():
            if modele:
                entropie_predictive = 0
                total_observations = 0
                contextes_analyses = 0

                for contexte, resultats in modele.items():
                    total_contexte = sum(resultats.values())
                    if total_contexte >= 3:  # Minimum pour statistiques fiables
                        contextes_analyses += 1

                        # Probabilités a posteriori avec lissage de Laplace
                        total_avec_lissage = total_contexte + 3  # 3 résultats possibles
                        probs = {
                            'BANKER': (resultats.get('BANKER', 0) + 1) / total_avec_lissage,
                            'PLAYER': (resultats.get('PLAYER', 0) + 1) / total_avec_lissage,
                            'TIE': (resultats.get('TIE', 0) + 1) / total_avec_lissage
                        }

                        # Entropie conditionnelle H(INDEX3|contexte)
                        entropie_locale = -sum(p * np.log2(p) for p in probs.values() if p > 0)

                        # Pondérer par fréquence du contexte
                        poids = total_contexte
                        entropie_predictive += poids * entropie_locale
                        total_observations += poids

                if total_observations > 0:
                    entropie_moyenne = entropie_predictive / total_observations
                    entropie_max = np.log2(3)  # 3 résultats possibles (BANKER, PLAYER, TIE)
                    predictibilite = max(0, 1 - (entropie_moyenne / entropie_max))

                    print(f"  {nom_modele:25} : {predictibilite:.3f} "
                          f"({contextes_analyses:,} contextes, {total_observations:,} observations)")

                    self.resultats_bayesien[nom_modele] = {
                        'predictibilite': predictibilite,
                        'entropie_moyenne': entropie_moyenne,
                        'contextes_analyses': contextes_analyses,
                        'total_observations': total_observations,
                        'details_contextes': dict(list(modele.items())[:5])  # Top 5 contextes pour debug
                    }

        # Analyse des patterns les plus prédictibles
        print("\n🎯 TOP PATTERNS PRÉDICTIBLES :")

        # Analyser le modèle le plus riche (index1_index2_vers_index3)
        modele_riche = modeles['index1_index2_vers_index3']
        patterns_predictibles = []

        for contexte, resultats in modele_riche.items():
            total = sum(resultats.values())
            if total >= 10:  # Minimum pour fiabilité
                # Trouver le résultat le plus probable
                resultat_max = max(resultats.items(), key=lambda x: x[1])
                probabilite_max = resultat_max[1] / total

                if probabilite_max > 0.6:  # Seuil de prédictibilité
                    patterns_predictibles.append({
                        'contexte': contexte,
                        'resultat': resultat_max[0],
                        'probabilite': probabilite_max,
                        'occurrences': total
                    })

        # Trier par prédictibilité
        patterns_predictibles.sort(key=lambda x: x['probabilite'], reverse=True)

        if patterns_predictibles:
            for i, pattern in enumerate(patterns_predictibles[:10]):  # Top 10
                ctx = pattern['contexte']
                print(f"  {i+1:2d}. INDEX1={ctx[0]}, INDEX2={ctx[1]} → {pattern['resultat']} "
                      f"({pattern['probabilite']:.1%}, {pattern['occurrences']} fois)")
        else:
            print("  Aucun pattern hautement prédictible détecté (seuil > 60%)")

            # Afficher les meilleurs patterns même s'ils sont < 60%
            patterns_tous = []
            for contexte, resultats in modele_riche.items():
                total = sum(resultats.values())
                if total >= 5:
                    resultat_max = max(resultats.items(), key=lambda x: x[1])
                    probabilite_max = resultat_max[1] / total
                    patterns_tous.append({
                        'contexte': contexte,
                        'resultat': resultat_max[0],
                        'probabilite': probabilite_max,
                        'occurrences': total
                    })

            patterns_tous.sort(key=lambda x: x['probabilite'], reverse=True)
            print("  MEILLEURS PATTERNS DISPONIBLES :")
            for i, pattern in enumerate(patterns_tous[:5]):
                ctx = pattern['contexte']
                print(f"    {i+1}. INDEX1={ctx[0]}, INDEX2={ctx[1]} → {pattern['resultat']} "
                      f"({pattern['probabilite']:.1%}, {pattern['occurrences']} fois)")

        self.resultats_bayesien['top_patterns'] = patterns_predictibles[:20] if patterns_predictibles else patterns_tous[:10]

    def generer_rapport_complet(self) -> str:
        """Génère un rapport complet des analyses F4 et Bayésiennes"""

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"rapport_micro_patterns_f4_bayesien_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("RAPPORT DÉTECTION MICRO-PATTERNS BACCARAT LUPASCO\n")
            f.write("=" * 70 + "\n")
            f.write(f"Généré le : {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Parties analysées : {self.nb_parties:,}\n")
            f.write(f"Mains analysées : {self.nb_mains_analysees:,}\n")
            f.write(f"Séquences valides : {len(self.sequences_index3):,}\n\n")

            # Section 1: Résultats F4
            f.write("1. ANALYSE F4 - DIMENSION FRACTALE PAR EMBEDDING\n")
            f.write("-" * 50 + "\n")

            if self.resultats_f4:
                for key, value in self.resultats_f4.items():
                    if key.startswith('embedding_'):
                        f.write(f"{key.replace('_', ' ').title()} :\n")
                        f.write(f"  Moyenne : {value['moyenne']:.3f}\n")
                        f.write(f"  Écart-type : {value['std']:.3f}\n")
                        f.write(f"  Médiane : {value['mediane']:.3f}\n")
                        f.write(f"  Échantillons : {value['count']:,}\n\n")

                if 'lag_optimal' in self.resultats_f4:
                    lag_info = self.resultats_f4['lag_optimal']
                    f.write(f"Lag optimal moyen : {lag_info['moyenne']:.1f}\n")
                    f.write(f"Range : {lag_info['min']}-{lag_info['max']}\n\n")

            # Section 2: Résultats Bayésiens
            f.write("2. ANALYSE BAYÉSIENNE - PRÉDICTIBILITÉ MICRO-PATTERNS\n")
            f.write("-" * 50 + "\n")

            if self.resultats_bayesien:
                for modele, resultats in self.resultats_bayesien.items():
                    if modele != 'top_patterns':
                        f.write(f"{modele.replace('_', ' ').title()} :\n")
                        f.write(f"  Prédictibilité : {resultats['predictibilite']:.3f}\n")
                        f.write(f"  Contextes analysés : {resultats['contextes_analyses']:,}\n")
                        f.write(f"  Observations totales : {resultats['total_observations']:,}\n\n")

                # Top patterns
                if 'top_patterns' in self.resultats_bayesien:
                    f.write("TOP PATTERNS PRÉDICTIBLES :\n")
                    for i, pattern in enumerate(self.resultats_bayesien['top_patterns'][:15]):
                        ctx = pattern['contexte']
                        f.write(f"  {i+1:2d}. INDEX1={ctx[0]}, INDEX2={ctx[1]} → {pattern['resultat']} "
                               f"({pattern['probabilite']:.1%}, {pattern['occurrences']} fois)\n")

            f.write("\n" + "=" * 70 + "\n")
            f.write("Fin du rapport\n")

        print(f"✅ Rapport généré : {filename}")
        return filename

    def executer_analyse_complete(self, filename: str):
        """Exécute l'analyse complète F4 + Bayésienne"""

        debut = time.time()

        print("🚀 DÉTECTEUR MICRO-PATTERNS F4 + BAYÉSIEN")
        print("=" * 70)

        # Chargement des données
        if not self.charger_donnees(filename):
            print("❌ Échec du chargement")
            return

        # Analyse F4 - Dimension Fractale
        self.analyser_dimension_fractale_f4()

        # Analyse Bayésienne - Prédictibilité
        self.analyser_predictibilite_bayesienne()

        # Génération du rapport
        rapport_file = self.generer_rapport_complet()

        temps_total = time.time() - debut
        print(f"\n✅ ANALYSE TERMINÉE EN {temps_total:.1f}s")
        print(f"📄 Rapport : {rapport_file}")
        print("=" * 70)


def main():
    """Fonction principale"""

    # Fichier de données - CORRIGÉ pour utiliser le bon fichier
    filename = "dataset_baccarat_lupasco_20250703_212650_condensed.json"

    print(f"🔍 Recherche du fichier : {filename}")
    import os
    if not os.path.exists(filename):
        print(f"❌ Fichier non trouvé : {filename}")
        print("📁 Fichiers disponibles :")
        for f in os.listdir('.'):
            if f.endswith('_condensed.json'):
                print(f"  - {f}")
        return

    # Créer et exécuter l'analyseur
    detecteur = DetecteurMicroPatternsF4Bayesien()
    detecteur.executer_analyse_complete(filename)


if __name__ == "__main__":
    main()
