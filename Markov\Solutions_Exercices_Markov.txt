SOLUTIONS DES EXERCICES - CHAÎNES DE MARKOV
================================================================================

RÉFÉRENCE : 2025_07_02_9ed09eec91a233259464g.tex - Section 5.8 Exercices

================================================================================
EXERCICE 64 : CHAÎNE DU MAXIMUM
================================================================================

ÉNONCÉ (Lignes 2318-2323) :
Soient 𝕏 = {0,1,...,m} et (U_n)_{n≥1} suite i.i.d. uniforme sur {1,...,m}.
X_0 = 0 et X_{n+1} = max(X_n, U_{n+1}) pour n ≥ 0.

SOLUTION :

(a) Propriété de Markov et matrice de transition :

DÉMONSTRATION que (X_n) est une chaîne de Markov :
Pour tout n ≥ 0 et x_0,...,x_n, y ∈ 𝕏 :
ℙ(X_{n+1}=y | X_0=x_0,...,X_n=x_n) = ℙ(max(x_n, U_{n+1})=y | X_n=x_n)

Puisque U_{n+1} est indépendant de X_0,...,X_n, ceci ne dépend que de x_n.
Donc (X_n) est une chaîne de Markov.

MATRICE DE TRANSITION P :
Pour x,y ∈ 𝕏 = {0,1,...,m} :

P_{x,y} = ℙ(X_{n+1}=y | X_n=x) = ℙ(max(x, U_{n+1})=y)

Cas 1 : Si y < x, alors max(x, U_{n+1}) ≥ x > y, donc P_{x,y} = 0

Cas 2 : Si y = x, alors max(x, U_{n+1}) = x ssi U_{n+1} ≤ x
        P_{x,x} = ℙ(U_{n+1} ≤ x) = x/m

Cas 3 : Si y > x, alors max(x, U_{n+1}) = y ssi U_{n+1} = y
        P_{x,y} = ℙ(U_{n+1} = y) = 1/m

MATRICE DE TRANSITION :
P_{x,y} = {
  0      si y < x
  x/m    si y = x  
  1/m    si y > x
}

(b) Graphe de la matrice stochastique pour m petit :

Pour m = 3, 𝕏 = {0,1,2,3} :

État 0 → États {0,1,2,3} avec probabilités {0, 1/3, 1/3, 1/3}
État 1 → États {1,2,3} avec probabilités {1/3, 1/3, 1/3}
État 2 → États {2,3} avec probabilités {2/3, 1/3}  
État 3 → État {3} avec probabilité {1}

Le graphe montre que :
- Tous les états communiquent vers les états supérieurs
- L'état m est absorbant
- La chaîne est irréductible jusqu'à absorption en m

(c) Classification des états :

ÉTATS TRANSIENTS : {0,1,...,m-1}
Démonstration : Pour tout x < m, il existe une probabilité positive de passer à un état y > x, et une fois dans un état supérieur, on ne peut plus revenir à x.

Plus précisément, pour x < m :
ℙ_x(τ_x < ∞) = ℙ_x(retour à x) < 1

Car ℙ_x(X_1 > x) = (m-x)/m > 0, et si X_1 > x, on ne peut plus revenir à x.

ÉTAT RÉCURRENT : {m}
L'état m est absorbant : P_{m,m} = 1, donc récurrent.

(d) Calcul de P² pour m = 4 :

𝕏 = {0,1,2,3,4}

Matrice P :
P = [0    1/4  1/4  1/4  1/4]
    [0    1/4  1/4  1/4  1/4]  
    [0    0    2/4  1/4  1/4]
    [0    0    0    3/4  1/4]
    [0    0    0    0    1  ]

Calcul de P² = P × P :

P²_{0,0} = 0
P²_{0,1} = 0×0 + (1/4)×0 + (1/4)×0 + (1/4)×0 + (1/4)×0 = 0
P²_{0,2} = 0×(1/4) + (1/4)×(1/4) + (1/4)×0 + (1/4)×0 + (1/4)×0 = 1/16
P²_{0,3} = 0×(1/4) + (1/4)×(1/4) + (1/4)×(1/4) + (1/4)×0 + (1/4)×0 = 2/16 = 1/8
P²_{0,4} = 0×(1/4) + (1/4)×(1/4) + (1/4)×(1/4) + (1/4)×(1/4) + (1/4)×0 = 3/16

Et ainsi de suite...

P² = [0   0    1/16  1/8   3/16  ...]
     [0   0    1/16  1/8   3/16  ...]
     [0   0    1/4   3/8   5/16  ...]
     [0   0    0     9/16  7/16  ]
     [0   0    0     0     1     ]

================================================================================
EXERCICE 65 : MARCHE ALÉATOIRE SIMPLE
================================================================================

ÉNONCÉ (Lignes 2324-2336) :
(ξ_n)_{n≥1} suite i.i.d. sur {-1,1} avec ℙ(ξ_n = -1) = p ∈ [0,1].
X_n = ∑_{k=1}^n ξ_k (marche aléatoire simple).

SOLUTION :

(a) Propriété de Markov :

X'_n = ∑_{k=1}^n ξ_k avec X'_0 = 0 est une chaîne de Markov sur 𝕏' = ℤ.

MATRICE DE TRANSITION P' :
P'_{x,y} = ℙ(X'_{n+1} = y | X'_n = x) = ℙ(x + ξ_{n+1} = y)

Donc : P'_{x,y} = {
  p      si y = x-1
  1-p    si y = x+1  
  0      sinon
}

PROBABILITÉ INITIALE : ρ'(0) = 1, ρ'(x) = 0 pour x ≠ 0.

(b) Marche avec condition initiale générale :

X_0 = x_0 ∈ ℤ, X_{n+1} = X_n + ξ_{n+1}

Ceci définit une CM(ℤ, P, ρ) où :
- 𝕏 = ℤ  
- P_{x,y} = P'_{x,y} (même matrice de transition)
- ρ(x_0) = 1, ρ(x) = 0 pour x ≠ x_0

(c) Irréductibilité pour p ∈ ]0,1[ :

Pour tout x,y ∈ ℤ, montrons que x → y.

Si y > x : Il faut faire (y-x) pas vers la droite.
Probabilité > 0 car chaque pas à droite a probabilité 1-p > 0.

Si y < x : Il faut faire (x-y) pas vers la gauche.  
Probabilité > 0 car chaque pas à gauche a probabilité p > 0.

Si y = x : Évident avec n = 0.

Donc la chaîne est irréductible.

(d) Récurrence vs transience :

CAS p = 1/2 (marche symétrique) :
La marche aléatoire simple symétrique sur ℤ est récurrente.

Démonstration : Utilisons le critère ∑_{n≥0} P^n(0,0) = ∞.

P^{2n}(0,0) = C_{2n}^n (1/2)^{2n} = C_{2n}^n / 4^n

Par la formule de Stirling : C_{2n}^n ~ 4^n / √(πn)

Donc P^{2n}(0,0) ~ 1/√(πn)

∑_{n≥1} P^{2n}(0,0) ~ ∑_{n≥1} 1/√(πn) = ∞

Donc l'état 0 est récurrent, et par irréductibilité, tous les états sont récurrents.

CAS p ≠ 1/2 (marche asymétrique) :
La marche est transiente.

Démonstration : Soit q = 1-p. Sans perte de généralité, supposons p < 1/2 (donc q > 1/2).

La probabilité de retour à 0 partant de 0 est :
ℙ_0(τ_0 < ∞) = 2p/q < 1 (formule classique)

Donc l'état 0 est transient.

(e) Problème de la ruine du joueur :

Pour x ∈ [a,b] ∩ ℤ, h(x) = ℙ_x(τ_a^0 < τ_b^0).

CAS p = 1/2 :
Équation : h(x) = (1/2)h(x-1) + (1/2)h(x+1) pour x ∈ [a+1,b-1]
Conditions aux bords : h(a) = 1, h(b) = 0

Solution : h(x) = (b-x)/(b-a)

Vérification : 
- h(a) = (b-a)/(b-a) = 1 ✓
- h(b) = (b-b)/(b-a) = 0 ✓  
- h(x) = (1/2)[(b-(x-1))/(b-a)] + (1/2)[(b-(x+1))/(b-a)] = (b-x)/(b-a) ✓

CAS p ≠ 1/2 :
Soit r = p/(1-p). L'équation devient :
h(x) = ph(x-1) + (1-p)h(x+1)

Solution générale : h(x) = A + Br^x

Conditions aux bords donnent :
A + Br^a = 1
A + Br^b = 0

Résolution :
A = -r^b/(r^b - r^a), B = 1/(r^b - r^a)

Donc : h(x) = (r^b - r^x)/(r^b - r^a)

(f) Interprétation - Ruine du joueur :

Un joueur commence avec une fortune x ∈ [a,b].
À chaque partie :
- Il gagne 1 avec probabilité 1-p  
- Il perd 1 avec probabilité p

h(x) = probabilité que sa fortune atteigne a (ruine) avant d'atteindre b.

Pour p = 1/2 : La probabilité de ruine dépend linéairement de la position initiale.
Pour p > 1/2 : Le jeu est défavorable, la probabilité de ruine augmente.
Pour p < 1/2 : Le jeu est favorable, la probabilité de ruine diminue.

================================================================================
EXERCICE 66 : CHAÎNE AVEC RETOUR À 1
================================================================================

ÉNONCÉ (Lignes 2343-2351) :
𝕏 = ℕ_{>0} = {1,2,3,...}
P_{x,y} = {x/(x+1) si y=1; 1/(x+1) si y=x+1; 0 sinon}

SOLUTION :

(a) Vérification que p = (p_x)_{x≥1} avec p_x = 1/((e-1)x!) est un vecteur de probabilité :

Il faut vérifier : ∑_{x≥1} p_x = 1 et p_x ≥ 0.

Positivité : Évidente car p_x = 1/((e-1)x!) > 0.

Normalisation :
∑_{x≥1} p_x = ∑_{x≥1} 1/((e-1)x!) = 1/(e-1) ∑_{x≥1} 1/x!

Or : e = ∑_{x≥0} 1/x! = 1 + ∑_{x≥1} 1/x!

Donc : ∑_{x≥1} 1/x! = e - 1

Par conséquent : ∑_{x≥1} p_x = 1/(e-1) × (e-1) = 1 ✓

(b) Invariance de p pour P :

Il faut vérifier : ∑_{x≥1} p_x P_{x,y} = p_y pour tout y ≥ 1.

Pour y = 1 :
∑_{x≥1} p_x P_{x,1} = ∑_{x≥1} p_x × x/(x+1) = ∑_{x≥1} 1/((e-1)x!) × x/(x+1)
= 1/(e-1) ∑_{x≥1} x/((x+1)x!) = 1/(e-1) ∑_{x≥1} 1/((x+1)(x-1)!)
= 1/(e-1) ∑_{x≥1} 1/(x × (x-1)!) = 1/(e-1) ∑_{x≥1} 1/x!

Utilisant l'astuce x/(x+1) = 1 - 1/(x+1) :
= 1/(e-1) [∑_{x≥1} 1/x! - ∑_{x≥1} 1/((x+1)x!)]
= 1/(e-1) [∑_{x≥1} 1/x! - ∑_{x≥2} 1/(x(x-1)!)]
= 1/(e-1) [∑_{x≥1} 1/x! - ∑_{x≥2} 1/x!]
= 1/(e-1) × 1/1! = 1/(e-1) = p_1 ✓

Pour y ≥ 2 :
∑_{x≥1} p_x P_{x,y} = p_{y-1} P_{y-1,y} = p_{y-1} × 1/y
= 1/((e-1)(y-1)!) × 1/y = 1/((e-1)y!) = p_y ✓

(c) Classes de communication :

Tous les états communiquent :
- Depuis tout état x ≥ 2, on peut atteindre 1 avec probabilité positive
- Depuis 1, on peut atteindre tout état y ≥ 2 en y-1 étapes

Donc il y a une seule classe de communication : 𝕏 = {1,2,3,...}.

================================================================================
RÉSUMÉ DES TECHNIQUES DE RÉSOLUTION
================================================================================

1. VÉRIFICATION PROPRIÉTÉ DE MARKOV : Montrer l'indépendance conditionnelle
2. CALCUL MATRICE DE TRANSITION : Analyser tous les cas possibles  
3. CLASSIFICATION DES ÉTATS : Utiliser les critères de récurrence/transience
4. PROBABILITÉS INVARIANTES : Résoudre πP = π
5. PROBLÈMES DE PREMIER PASSAGE : Équations récurrentes avec conditions aux bords
6. CALCULS DE PUISSANCES : Utiliser la structure de la matrice

================================================================================
TOTAL : 3 EXERCICES RÉSOLUS COMPLÈTEMENT
================================================================================
