#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST DU SYSTÈME DE PRÉDICTION LUPASCO
====================================

Script de test pour évaluer le système de prédiction sur le dataset de 1M parties
Optimisé pour traiter de gros volumes de données

Auteur: Augment Agent
Date: 2025-07-03
"""

import json
import sys
import time
from predicteur_lupasco import SystemePredictionLupasco, PredictionResult
from typing import List, Dict, Any
import gc


class TesteurPredicteurLupasco:
    """Testeur optimisé pour gros datasets"""
    
    def __init__(self):
        self.systeme = SystemePredictionLupasco()
        
    def charger_donnees_streaming(self, filename: str, max_parties: int = None) -> List[Dict]:
        """Charge les données avec gestion mémoire optimisée"""
        
        print(f"📂 Chargement streaming : {filename}")
        
        try:
            with open(filename, 'r', encoding='utf-8', buffering=64*1024) as f:
                content = f.read()
            
            # Correction du format JSON mal formé si nécessaire
            if content.startswith(',{"partie_number"'):
                print("🔧 Correction du format JSON...")
                if content.endswith(']}'):
                    content = content[:-2]
                content = '{"parties_condensees": [' + content[1:] + ']}'
            
            print("🔄 Parsing JSON...")
            data = json.loads(content)
            del content  # Libérer la mémoire
            gc.collect()
            
            parties = data.get('parties_condensees', [])
            
            if max_parties:
                parties = parties[:max_parties]
            
            print(f"✅ {len(parties):,} parties chargées")
            return parties
            
        except Exception as e:
            print(f"❌ Erreur chargement : {e}")
            return []
    
    def test_echantillon_rapide(self, parties: List[Dict], taille_echantillon: int = 1000):
        """Test rapide sur un échantillon représentatif"""
        
        print(f"\n🚀 TEST ÉCHANTILLON RAPIDE")
        print(f"📊 Échantillon : {taille_echantillon:,} parties")
        print("=" * 60)
        
        # Sélectionner un échantillon représentatif
        step = max(1, len(parties) // taille_echantillon)
        echantillon = parties[::step][:taille_echantillon]
        
        print(f"✅ Échantillon sélectionné : {len(echantillon):,} parties (step={step})")
        
        # Évaluer l'échantillon
        debut = time.time()
        resultats = self.systeme.evaluer_predictions(echantillon, debut_evaluation=10)
        duree = time.time() - debut
        
        print(f"\n⏱️  Durée d'évaluation : {duree:.1f}s")
        self.systeme.afficher_rapport_evaluation(resultats)
        
        return resultats
    
    def test_progression_apprentissage(self, parties: List[Dict], tranches: List[int] = None):
        """Test de la progression de l'apprentissage"""
        
        if tranches is None:
            tranches = [100, 500, 1000, 5000, 10000]
        
        print(f"\n📈 TEST PROGRESSION APPRENTISSAGE")
        print("=" * 60)
        
        resultats_progression = []
        
        for taille in tranches:
            if taille > len(parties):
                continue
                
            print(f"\n🔍 Test avec {taille:,} parties...")
            echantillon = parties[:taille]
            
            debut = time.time()
            resultats = self.systeme.evaluer_predictions(echantillon, debut_evaluation=10)
            duree = time.time() - debut
            
            taux_reussite = resultats['taux_reussite_global']
            
            resultats_progression.append({
                'taille': taille,
                'taux_reussite': taux_reussite,
                'duree': duree,
                'predictions_totales': resultats['total_predictions']
            })
            
            print(f"   Taux de réussite : {taux_reussite:.1%}")
            print(f"   Durée : {duree:.1f}s")
        
        # Afficher la progression
        print(f"\n📊 RÉSUMÉ PROGRESSION :")
        print("-" * 50)
        print("Parties    | Taux réussite | Prédictions | Durée")
        print("-" * 50)
        for r in resultats_progression:
            print(f"{r['taille']:8,} | {r['taux_reussite']:11.1%} | {r['predictions_totales']:10,} | {r['duree']:5.1f}s")
        
        return resultats_progression
    
    def test_patterns_specifiques(self, parties: List[Dict], nb_parties_test: int = 5000):
        """Test des patterns spécifiques identifiés"""
        
        print(f"\n🔍 TEST PATTERNS SPÉCIFIQUES")
        print(f"📊 Parties analysées : {nb_parties_test:,}")
        print("=" * 60)
        
        # Analyser les patterns dans l'échantillon
        echantillon = parties[:nb_parties_test]
        
        patterns_stats = {
            'sequences_CCC_to_BANKER': {'total': 0, 'correct': 0},
            'sequences_BBB_to_PLAYER': {'total': 0, 'correct': 0},
            'regulation_active': {'total': 0, 'correct': 0},
            'index2_C_to_BANKER': {'total': 0, 'correct': 0},
            'index2_B_to_PLAYER': {'total': 0, 'correct': 0}
        }
        
        for partie in echantillon:
            mains = self.systeme.predicteur.analyseur.extraire_mains_valides(partie)
            
            for position in range(10, len(mains)):
                # Analyser les patterns avant cette position
                mains_historique = mains[:position]
                main_actuelle = mains[position]
                
                if main_actuelle.index3 not in ['BANKER', 'PLAYER']:
                    continue
                
                # Pattern : séquence CCC → BANKER
                if len(mains_historique) >= 3:
                    derniers_index2 = [m.index2 for m in mains_historique[-3:]]
                    if derniers_index2 == ['C', 'C', 'C']:
                        patterns_stats['sequences_CCC_to_BANKER']['total'] += 1
                        if main_actuelle.index3 == 'BANKER':
                            patterns_stats['sequences_CCC_to_BANKER']['correct'] += 1
                
                # Pattern : séquence BBB → PLAYER
                if len(mains_historique) >= 3:
                    derniers_index2 = [m.index2 for m in mains_historique[-3:]]
                    if derniers_index2 == ['B', 'B', 'B']:
                        patterns_stats['sequences_BBB_to_PLAYER']['total'] += 1
                        if main_actuelle.index3 == 'PLAYER':
                            patterns_stats['sequences_BBB_to_PLAYER']['correct'] += 1
                
                # Pattern : INDEX2=C → BANKER (général)
                if len(mains_historique) >= 1:
                    dernier_index2 = mains_historique[-1].index2
                    if dernier_index2 == 'C':
                        patterns_stats['index2_C_to_BANKER']['total'] += 1
                        if main_actuelle.index3 == 'BANKER':
                            patterns_stats['index2_C_to_BANKER']['correct'] += 1
                
                # Pattern : INDEX2=B → PLAYER (général)
                if len(mains_historique) >= 1:
                    dernier_index2 = mains_historique[-1].index2
                    if dernier_index2 == 'B':
                        patterns_stats['index2_B_to_PLAYER']['total'] += 1
                        if main_actuelle.index3 == 'PLAYER':
                            patterns_stats['index2_B_to_PLAYER']['correct'] += 1
        
        # Afficher les résultats
        print("Pattern                    | Occurrences | Taux réussite")
        print("-" * 55)
        for pattern, stats in patterns_stats.items():
            if stats['total'] > 0:
                taux = stats['correct'] / stats['total']
                print(f"{pattern:25s} | {stats['total']:10,} | {taux:11.1%}")
            else:
                print(f"{pattern:25s} | {stats['total']:10,} | {'N/A':>11s}")
        
        return patterns_stats
    
    def test_performance_methodes(self, parties: List[Dict], nb_parties_test: int = 2000):
        """Test comparatif des différentes méthodes"""
        
        print(f"\n⚡ TEST PERFORMANCE MÉTHODES")
        print(f"📊 Parties testées : {nb_parties_test:,}")
        print("=" * 60)
        
        echantillon = parties[:nb_parties_test]
        
        # Test avec différentes configurations
        configurations = [
            {'nom': 'Markov seul', 'force_methode': 'markov'},
            {'nom': 'Fractal seul', 'force_methode': 'fractal'},
            {'nom': 'Fusion adaptative', 'force_methode': 'fusion'},
        ]
        
        resultats_methodes = []
        
        for config in configurations:
            print(f"\n🔧 Test : {config['nom']}")
            
            # Modifier temporairement le système pour forcer la méthode
            # (simulation - dans une vraie implémentation, on modifierait la classe)
            
            debut = time.time()
            resultats = self.systeme.evaluer_predictions(echantillon, debut_evaluation=10)
            duree = time.time() - debut
            
            resultats_methodes.append({
                'nom': config['nom'],
                'taux_reussite': resultats['taux_reussite_global'],
                'duree': duree,
                'predictions': resultats['total_predictions']
            })
            
            print(f"   Taux : {resultats['taux_reussite_global']:.1%} | Durée : {duree:.1f}s")
        
        # Résumé comparatif
        print(f"\n📊 COMPARAISON MÉTHODES :")
        print("-" * 50)
        print("Méthode              | Taux réussite | Durée")
        print("-" * 50)
        for r in resultats_methodes:
            print(f"{r['nom']:19s} | {r['taux_reussite']:11.1%} | {r['duree']:5.1f}s")
        
        return resultats_methodes


def main():
    """Fonction principale de test"""
    
    print("🎯 TESTEUR SYSTÈME PRÉDICTION LUPASCO")
    print("=" * 60)
    
    # Initialiser le testeur
    testeur = TesteurPredicteurLupasco()
    
    # Fichier de données
    filename = "dataset_baccarat_lupasco_20250703_190003_condensed.json"
    
    # Charger un échantillon pour les tests
    print(f"📂 Chargement des données...")
    parties = testeur.charger_donnees_streaming(filename, max_parties=50000)  # Limiter pour les tests
    
    if not parties:
        print("❌ Aucune donnée chargée")
        return
    
    # Tests séquentiels
    try:
        # 1. Test échantillon rapide
        testeur.test_echantillon_rapide(parties, taille_echantillon=1000)
        
        # 2. Test progression apprentissage
        testeur.test_progression_apprentissage(parties, tranches=[100, 500, 1000, 2000, 5000])
        
        # 3. Test patterns spécifiques
        testeur.test_patterns_specifiques(parties, nb_parties_test=5000)
        
        # 4. Test performance méthodes
        testeur.test_performance_methodes(parties, nb_parties_test=2000)
        
        print(f"\n✅ TESTS TERMINÉS AVEC SUCCÈS")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur durant les tests : {e}")


if __name__ == "__main__":
    main()
