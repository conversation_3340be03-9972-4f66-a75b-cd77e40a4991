#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SYSTÈME DE PRÉDICTION LUPASCO - INDEX3 (BANKER/PLAYER)
=====================================================

Système hybride Markov-Fractale pour prédire INDEX3 dans le système Lupasco
Basé sur l'analyse des patterns déterministes cachés dans le générateur

Auteur: Augment Agent
Date: 2025-07-03
"""

import json
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict, Counter
import math
from datetime import datetime


@dataclass
class MainLupasco:
    """Structure d'une main Lupasco"""
    main_number: Optional[int]
    manche_pb_number: Optional[int]
    index1: int
    index2: str
    index3: str
    index5: str


@dataclass
class PredictionResult:
    """Résultat d'une prédiction"""
    prediction: str
    confidence: float
    method_used: str
    patterns_detected: List[str]
    probabilities: Dict[str, float]


class AnalyseurPatternsLupasco:
    """Analyseur de patterns dans les séquences Lupasco"""
    
    def __init__(self):
        # Probabilités observées dans le rapport
        self.probas_index2_to_index3 = {
            'A': {'BANKER': 0.453, 'PLAYER': 0.453, 'TIE': 0.094},
            'B': {'BANKER': 0.410, 'PLAYER': 0.487, 'TIE': 0.103}, 
            'C': {'BANKER': 0.517, 'PLAYER': 0.395, 'TIE': 0.088}
        }
        
        # Patterns de transition INDEX1
        self.transitions_index1 = {
            ('A', 0): 0,  # A conserve INDEX1
            ('A', 1): 1,
            ('B', 0): 0,  # B conserve INDEX1
            ('B', 1): 1,
            ('C', 0): 1,  # C bascule INDEX1
            ('C', 1): 0
        }
    
    def extraire_mains_valides(self, partie: Dict) -> List[MainLupasco]:
        """Extrait les mains valides (exclut dummy)"""
        mains = []
        for main_data in partie['mains_condensees']:
            if main_data['main_number'] is not None:
                main = MainLupasco(
                    main_number=main_data['main_number'],
                    manche_pb_number=main_data['manche_pb_number'],
                    index1=main_data['index1'],
                    index2=main_data['index2'],
                    index3=main_data['index3'],
                    index5=main_data['index5']
                )
                mains.append(main)
        return mains
    
    def analyser_sequences_index2(self, mains: List[MainLupasco], longueur: int = 3) -> List[str]:
        """Analyse les séquences d'INDEX2"""
        if len(mains) < longueur:
            return []
        
        sequences = []
        for i in range(len(mains) - longueur + 1):
            sequence = ''.join([mains[i + j].index2 for j in range(longueur)])
            sequences.append(sequence)
        
        return sequences
    
    def analyser_cycles_sync(self, mains: List[MainLupasco]) -> Dict[str, Any]:
        """Analyse les cycles SYNC/DESYNC"""
        if len(mains) < 2:
            return {'cycles_detectes': [], 'longueur_moyenne': 0}
        
        # Détecter les changements d'état SYNC
        changements = []
        for i in range(1, len(mains)):
            if mains[i].index1 != mains[i-1].index1:
                changements.append(i)
        
        # Calculer les longueurs de cycles
        longueurs_cycles = []
        if len(changements) > 1:
            for i in range(1, len(changements)):
                longueur = changements[i] - changements[i-1]
                longueurs_cycles.append(longueur)
        
        return {
            'changements': changements,
            'longueurs_cycles': longueurs_cycles,
            'longueur_moyenne': np.mean(longueurs_cycles) if longueurs_cycles else 0,
            'stabilite': len(changements) / len(mains) if mains else 0
        }
    
    def detecter_patterns_regulation(self, mains: List[MainLupasco]) -> Dict[str, Any]:
        """Détecte les patterns de régulation du système"""
        if len(mains) < 10:
            return {'regulation_active': False}
        
        # Analyser les déséquilibres BANKER/PLAYER
        banker_count = sum(1 for m in mains if m.index3 == 'BANKER')
        player_count = sum(1 for m in mains if m.index3 == 'PLAYER')
        total_bp = banker_count + player_count
        
        if total_bp == 0:
            return {'regulation_active': False}
        
        desequilibre = abs(banker_count - player_count) / total_bp
        
        # Analyser les dernières 10 mains pour tendance de correction
        dernieres_mains = mains[-10:]
        banker_recent = sum(1 for m in dernieres_mains if m.index3 == 'BANKER')
        player_recent = sum(1 for m in dernieres_mains if m.index3 == 'PLAYER')
        
        # Détecter si le système corrige un déséquilibre
        correction_active = False
        if banker_count > player_count and player_recent > banker_recent:
            correction_active = True
        elif player_count > banker_count and banker_recent > player_recent:
            correction_active = True
        
        return {
            'regulation_active': correction_active,
            'desequilibre_global': desequilibre,
            'banker_count': banker_count,
            'player_count': player_count,
            'tendance_correction': 'PLAYER' if player_recent > banker_recent else 'BANKER'
        }
    
    def analyser_patterns_index2_to_index3(self, mains: List[MainLupasco]) -> Dict[str, Dict[str, float]]:
        """Analyse les transitions INDEX2 → INDEX3 dans les données"""
        transitions = defaultdict(lambda: defaultdict(int))
        
        for main in mains:
            if main.index3 in ['BANKER', 'PLAYER']:  # Exclure TIE pour l'analyse
                transitions[main.index2][main.index3] += 1
        
        # Convertir en probabilités
        probas = {}
        for index2, counts in transitions.items():
            total = sum(counts.values())
            if total > 0:
                probas[index2] = {
                    'BANKER': counts['BANKER'] / total,
                    'PLAYER': counts['PLAYER'] / total
                }
        
        return probas


class PredicteurMarkovLupasco:
    """Prédicteur basé sur les chaînes de Markov"""
    
    def __init__(self, analyseur: AnalyseurPatternsLupasco):
        self.analyseur = analyseur
        
    def predire_index2_suivant(self, mains: List[MainLupasco]) -> str:
        """Prédit INDEX2 de la main suivante basé sur INDEX1 actuel"""
        if not mains:
            return 'A'  # Défaut
        
        derniere_main = mains[-1]
        index1_actuel = derniere_main.index1
        index2_actuel = derniere_main.index2
        
        # Calculer INDEX1 suivant selon les règles Lupasco
        index1_suivant = self.analyseur.transitions_index1.get((index2_actuel, index1_actuel), index1_actuel)
        
        # Analyser les patterns récents d'INDEX2
        sequences_index2 = [m.index2 for m in mains[-10:]]  # 10 dernières mains
        counter_index2 = Counter(sequences_index2)
        
        # Prédiction basée sur la fréquence et l'équilibrage
        total = sum(counter_index2.values())
        if total > 0:
            # Favoriser l'INDEX2 le moins fréquent pour équilibrage
            min_freq = min(counter_index2.values())
            candidats = [idx2 for idx2, freq in counter_index2.items() if freq == min_freq]
            return candidats[0] if candidats else 'A'
        
        return 'A'
    
    def predire_index3_markov(self, mains: List[MainLupasco]) -> PredictionResult:
        """Prédiction INDEX3 par analyse Markov"""
        if not mains:
            return PredictionResult('BANKER', 0.5, 'markov_default', [], {'BANKER': 0.5, 'PLAYER': 0.5})
        
        # Prédire INDEX2 suivant
        index2_predit = self.predire_index2_suivant(mains)
        
        # Utiliser les probabilités observées
        probas_base = self.analyseur.probas_index2_to_index3.get(index2_predit, 
                                                                {'BANKER': 0.5, 'PLAYER': 0.5})
        
        # Analyser les patterns de régulation
        regulation = self.analyseur.detecter_patterns_regulation(mains)
        
        # Ajuster les probabilités selon la régulation
        probas_ajustees = probas_base.copy()
        patterns_detectes = [f'index2_predit_{index2_predit}']
        
        if regulation['regulation_active']:
            # Favoriser la tendance de correction
            tendance = regulation['tendance_correction']
            if tendance == 'BANKER':
                probas_ajustees['BANKER'] *= 1.2
                probas_ajustees['PLAYER'] *= 0.8
            else:
                probas_ajustees['PLAYER'] *= 1.2
                probas_ajustees['BANKER'] *= 0.8
            patterns_detectes.append('regulation_active')
        
        # Normaliser les probabilités
        total_prob = probas_ajustees['BANKER'] + probas_ajustees['PLAYER']
        probas_ajustees['BANKER'] /= total_prob
        probas_ajustees['PLAYER'] /= total_prob
        
        # Prédiction finale
        prediction = 'BANKER' if probas_ajustees['BANKER'] > probas_ajustees['PLAYER'] else 'PLAYER'
        confidence = max(probas_ajustees['BANKER'], probas_ajustees['PLAYER'])
        
        return PredictionResult(
            prediction=prediction,
            confidence=confidence,
            method_used='markov',
            patterns_detected=patterns_detectes,
            probabilities=probas_ajustees
        )


class PredicteurFractalLupasco:
    """Prédicteur basé sur l'analyse fractale"""
    
    def __init__(self):
        self.lag_optimal = 3  # Lag par défaut
        
    def creer_serie_temporelle(self, mains: List[MainLupasco]) -> List[int]:
        """Crée une série temporelle numérique à partir d'INDEX3"""
        mapping = {'BANKER': 1, 'PLAYER': -1, 'TIE': 0}
        return [mapping.get(main.index3, 0) for main in mains]
    
    def calculer_lag_optimal(self, serie: List[int]) -> int:
        """Calcule le lag optimal par autocorrélation"""
        if len(serie) < 6:
            return 3
        
        # Calculer l'autocorrélation pour différents lags
        max_lag = min(10, len(serie) // 2)
        autocorrelations = []
        
        for lag in range(1, max_lag + 1):
            if len(serie) > lag:
                corr = np.corrcoef(serie[:-lag], serie[lag:])[0, 1]
                autocorrelations.append(abs(corr) if not np.isnan(corr) else 0)
            else:
                autocorrelations.append(0)
        
        # Retourner le lag avec la plus forte autocorrélation
        if autocorrelations:
            return autocorrelations.index(max(autocorrelations)) + 1
        return 3
    
    def predire_index3_fractal(self, mains: List[MainLupasco]) -> PredictionResult:
        """Prédiction INDEX3 par analyse fractale"""
        if len(mains) < 5:
            return PredictionResult('BANKER', 0.5, 'fractal_insufficient_data', [], {'BANKER': 0.5, 'PLAYER': 0.5})
        
        # Créer la série temporelle
        serie = self.creer_serie_temporelle(mains)
        
        # Calculer le lag optimal
        lag_opt = self.calculer_lag_optimal(serie)
        
        # Analyser les patterns avec le lag optimal
        if len(serie) > lag_opt:
            # Prendre les derniers points pour prédiction
            pattern_actuel = serie[-lag_opt:]
            
            # Rechercher des patterns similaires dans l'historique
            patterns_similaires = []
            for i in range(len(serie) - lag_opt - 1):
                pattern_historique = serie[i:i + lag_opt]
                
                # Calculer la similarité (distance euclidienne)
                distance = np.linalg.norm(np.array(pattern_actuel) - np.array(pattern_historique))
                
                if distance < 1.5:  # Seuil de similarité
                    # Récupérer la valeur suivante
                    valeur_suivante = serie[i + lag_opt]
                    patterns_similaires.append(valeur_suivante)
            
            # Prédiction basée sur les patterns similaires
            if patterns_similaires:
                moyenne = np.mean(patterns_similaires)
                prediction = 'BANKER' if moyenne > 0 else 'PLAYER'
                confidence = min(0.9, 0.5 + abs(moyenne) * 0.3)
                
                return PredictionResult(
                    prediction=prediction,
                    confidence=confidence,
                    method_used='fractal',
                    patterns_detected=[f'lag_optimal_{lag_opt}', f'patterns_similaires_{len(patterns_similaires)}'],
                    probabilities={'BANKER': (1 + moyenne) / 2, 'PLAYER': (1 - moyenne) / 2}
                )
        
        # Fallback sur analyse de tendance
        banker_count = sum(1 for val in serie[-10:] if val == 1)
        player_count = sum(1 for val in serie[-10:] if val == -1)
        
        if banker_count > player_count:
            return PredictionResult('PLAYER', 0.6, 'fractal_trend_correction', ['trend_correction'], {'BANKER': 0.4, 'PLAYER': 0.6})
        else:
            return PredictionResult('BANKER', 0.6, 'fractal_trend_correction', ['trend_correction'], {'BANKER': 0.6, 'PLAYER': 0.4})


class PredicteurHybrideLupasco:
    """Prédicteur hybride combinant Markov et Fractal"""

    def __init__(self):
        self.analyseur = AnalyseurPatternsLupasco()
        self.markov = PredicteurMarkovLupasco(self.analyseur)
        self.fractal = PredicteurFractalLupasco()

    def analyser_contexte(self, mains: List[MainLupasco]) -> Dict[str, Any]:
        """Analyse le contexte pour choisir la meilleure méthode"""
        if len(mains) < 5:
            return {'methode_recommandee': 'markov', 'confiance_contexte': 0.5}

        # Analyser la stabilité des patterns
        cycles_sync = self.analyseur.analyser_cycles_sync(mains)
        regulation = self.analyseur.detecter_patterns_regulation(mains)

        # Analyser la complexité des séquences INDEX2
        sequences_index2 = [m.index2 for m in mains[-15:]]
        complexite = len(set(sequences_index2)) / len(sequences_index2) if sequences_index2 else 0

        contexte = {
            'stabilite_sync': 1 - cycles_sync['stabilite'],
            'regulation_active': regulation['regulation_active'],
            'complexite_sequences': complexite,
            'longueur_historique': len(mains),
            'desequilibre': regulation.get('desequilibre_global', 0)
        }

        # Choisir la méthode selon le contexte
        if contexte['regulation_active'] and contexte['stabilite_sync'] > 0.7:
            contexte['methode_recommandee'] = 'markov'
            contexte['confiance_contexte'] = 0.8
        elif contexte['complexite_sequences'] > 0.6 and contexte['longueur_historique'] > 15:
            contexte['methode_recommandee'] = 'fractal'
            contexte['confiance_contexte'] = 0.7
        else:
            contexte['methode_recommandee'] = 'fusion'
            contexte['confiance_contexte'] = 0.6

        return contexte

    def fusion_predictions(self, pred_markov: PredictionResult, pred_fractal: PredictionResult,
                          contexte: Dict[str, Any]) -> PredictionResult:
        """Fusionne les prédictions Markov et Fractal"""

        # Poids selon le contexte
        if contexte['regulation_active']:
            poids_markov = 0.7
            poids_fractal = 0.3
        elif contexte['complexite_sequences'] > 0.6:
            poids_markov = 0.3
            poids_fractal = 0.7
        else:
            poids_markov = 0.5
            poids_fractal = 0.5

        # Fusion des probabilités
        probas_fusionnees = {}
        for outcome in ['BANKER', 'PLAYER']:
            prob_markov = pred_markov.probabilities.get(outcome, 0.5)
            prob_fractal = pred_fractal.probabilities.get(outcome, 0.5)
            probas_fusionnees[outcome] = (poids_markov * prob_markov + poids_fractal * prob_fractal)

        # Normaliser
        total = sum(probas_fusionnees.values())
        for outcome in probas_fusionnees:
            probas_fusionnees[outcome] /= total

        # Prédiction finale
        prediction = max(probas_fusionnees, key=probas_fusionnees.get)
        confidence = max(probas_fusionnees.values())

        # Combiner les patterns détectés
        patterns_combines = pred_markov.patterns_detected + pred_fractal.patterns_detected
        patterns_combines.append(f'fusion_markov_{poids_markov:.1f}_fractal_{poids_fractal:.1f}')

        return PredictionResult(
            prediction=prediction,
            confidence=confidence,
            method_used='fusion',
            patterns_detected=patterns_combines,
            probabilities=probas_fusionnees
        )

    def predire_index3(self, mains: List[MainLupasco]) -> PredictionResult:
        """Prédiction finale INDEX3 avec méthode optimale"""

        # Analyser le contexte
        contexte = self.analyser_contexte(mains)

        # Obtenir les prédictions individuelles
        pred_markov = self.markov.predire_index3_markov(mains)
        pred_fractal = self.fractal.predire_index3_fractal(mains)

        # Choisir la méthode selon le contexte
        if contexte['methode_recommandee'] == 'markov':
            pred_markov.patterns_detected.append('contexte_favorable_markov')
            return pred_markov
        elif contexte['methode_recommandee'] == 'fractal':
            pred_fractal.patterns_detected.append('contexte_favorable_fractal')
            return pred_fractal
        else:
            return self.fusion_predictions(pred_markov, pred_fractal, contexte)


class SystemePredictionLupasco:
    """Système principal de prédiction Lupasco"""

    def __init__(self):
        self.predicteur = PredicteurHybrideLupasco()
        self.historique_predictions = []

    def charger_donnees(self, filename: str) -> List[Dict]:
        """Charge les données depuis un fichier JSON"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data.get('parties_condensees', [])
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return []

    def predire_main_suivante(self, partie: Dict, position_actuelle: int) -> PredictionResult:
        """Prédit INDEX3 pour la main suivante dans une partie"""

        # Extraire les mains jusqu'à la position actuelle
        mains = self.predicteur.analyseur.extraire_mains_valides(partie)
        mains_historique = mains[:position_actuelle]

        if not mains_historique:
            return PredictionResult('BANKER', 0.5, 'no_history', [], {'BANKER': 0.5, 'PLAYER': 0.5})

        # Faire la prédiction
        prediction = self.predicteur.predire_index3(mains_historique)

        # Ajouter des informations contextuelles
        prediction.patterns_detected.append(f'position_{position_actuelle}')
        prediction.patterns_detected.append(f'partie_{partie["partie_number"]}')

        return prediction

    def evaluer_predictions(self, parties: List[Dict], debut_evaluation: int = 10) -> Dict[str, Any]:
        """Évalue la performance du système sur un ensemble de parties"""

        total_predictions = 0
        predictions_correctes = 0
        resultats_detailles = []

        print(f"🎯 ÉVALUATION DU SYSTÈME DE PRÉDICTION")
        print(f"📊 Parties à évaluer : {len(parties)}")
        print(f"🔍 Début évaluation à partir de la main {debut_evaluation}")
        print("=" * 60)

        for i, partie in enumerate(parties):
            mains = self.predicteur.analyseur.extraire_mains_valides(partie)

            if len(mains) <= debut_evaluation:
                continue

            # Évaluer les prédictions pour cette partie
            for position in range(debut_evaluation, len(mains)):
                # Prédire la main à cette position
                prediction = self.predire_main_suivante(partie, position)

                # Vérifier si la prédiction est correcte
                main_reelle = mains[position]
                correct = (prediction.prediction == main_reelle.index3)

                total_predictions += 1
                if correct:
                    predictions_correctes += 1

                resultats_detailles.append({
                    'partie': partie['partie_number'],
                    'position': position,
                    'prediction': prediction.prediction,
                    'reel': main_reelle.index3,
                    'correct': correct,
                    'confidence': prediction.confidence,
                    'methode': prediction.method_used,
                    'patterns': prediction.patterns_detected
                })

            # Affichage du progrès
            if (i + 1) % 1000 == 0:
                taux_actuel = predictions_correctes / total_predictions if total_predictions > 0 else 0
                print(f"🔄 Parties évaluées : {i + 1:,} | Taux de réussite actuel : {taux_actuel:.1%}")

        # Calculer les statistiques finales
        taux_reussite = predictions_correctes / total_predictions if total_predictions > 0 else 0

        # Analyser par méthode
        stats_par_methode = defaultdict(lambda: {'total': 0, 'correct': 0})
        for resultat in resultats_detailles:
            methode = resultat['methode']
            stats_par_methode[methode]['total'] += 1
            if resultat['correct']:
                stats_par_methode[methode]['correct'] += 1

        # Analyser par niveau de confiance
        stats_par_confiance = defaultdict(lambda: {'total': 0, 'correct': 0})
        for resultat in resultats_detailles:
            niveau_confiance = 'haute' if resultat['confidence'] > 0.7 else 'moyenne' if resultat['confidence'] > 0.6 else 'faible'
            stats_par_confiance[niveau_confiance]['total'] += 1
            if resultat['correct']:
                stats_par_confiance[niveau_confiance]['correct'] += 1

        return {
            'taux_reussite_global': taux_reussite,
            'total_predictions': total_predictions,
            'predictions_correctes': predictions_correctes,
            'stats_par_methode': dict(stats_par_methode),
            'stats_par_confiance': dict(stats_par_confiance),
            'resultats_detailles': resultats_detailles
        }

    def afficher_rapport_evaluation(self, resultats: Dict[str, Any]):
        """Affiche un rapport détaillé des résultats d'évaluation"""

        print(f"\n🎯 RAPPORT D'ÉVALUATION DU SYSTÈME LUPASCO")
        print("=" * 60)
        print(f"📊 Total prédictions : {resultats['total_predictions']:,}")
        print(f"✅ Prédictions correctes : {resultats['predictions_correctes']:,}")
        print(f"🎯 Taux de réussite global : {resultats['taux_reussite_global']:.1%}")

        print(f"\n📈 PERFORMANCE PAR MÉTHODE :")
        print("-" * 40)
        for methode, stats in resultats['stats_par_methode'].items():
            taux = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            print(f"• {methode:15s} : {taux:.1%} ({stats['correct']:,}/{stats['total']:,})")

        print(f"\n🎯 PERFORMANCE PAR CONFIANCE :")
        print("-" * 40)
        for niveau, stats in resultats['stats_par_confiance'].items():
            taux = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            print(f"• {niveau:15s} : {taux:.1%} ({stats['correct']:,}/{stats['total']:,})")

        # Analyser les patterns les plus performants
        patterns_performance = defaultdict(lambda: {'total': 0, 'correct': 0})
        for resultat in resultats['resultats_detailles']:
            for pattern in resultat['patterns']:
                patterns_performance[pattern]['total'] += 1
                if resultat['correct']:
                    patterns_performance[pattern]['correct'] += 1

        print(f"\n🔍 TOP 10 PATTERNS LES PLUS PERFORMANTS :")
        print("-" * 50)
        patterns_tries = sorted(patterns_performance.items(),
                               key=lambda x: x[1]['correct'] / x[1]['total'] if x[1]['total'] > 10 else 0,
                               reverse=True)

        for i, (pattern, stats) in enumerate(patterns_tries[:10]):
            if stats['total'] > 10:  # Seulement les patterns avec assez de données
                taux = stats['correct'] / stats['total']
                print(f"{i+1:2d}. {pattern:30s} : {taux:.1%} ({stats['correct']:,}/{stats['total']:,})")

    def tester_prediction_temps_reel(self, partie: Dict, affichage_detaille: bool = True):
        """Test en temps réel sur une partie complète"""

        mains = self.predicteur.analyseur.extraire_mains_valides(partie)

        print(f"\n🎮 TEST TEMPS RÉEL - PARTIE {partie['partie_number']}")
        print("=" * 70)
        print("Main | Prédiction | Réel     | ✓/✗ | Confiance | Méthode")
        print("-" * 70)

        predictions_correctes = 0
        total_predictions = 0

        for position in range(5, len(mains)):  # Commencer à partir de la main 5
            # Prédire la main suivante
            prediction = self.predire_main_suivante(partie, position)
            main_reelle = mains[position]

            # Vérifier si correct
            correct = (prediction.prediction == main_reelle.index3)
            if correct:
                predictions_correctes += 1
            total_predictions += 1

            # Affichage
            status = "✅" if correct else "❌"
            print(f"{position:4d} | {prediction.prediction:10s} | {main_reelle.index3:8s} | {status} | {prediction.confidence:.1%}     | {prediction.method_used}")

            if affichage_detaille and position <= 15:
                print(f"     Patterns: {', '.join(prediction.patterns_detected[:3])}")

        taux_reussite = predictions_correctes / total_predictions if total_predictions > 0 else 0
        print("-" * 70)
        print(f"🎯 Résultat : {predictions_correctes}/{total_predictions} ({taux_reussite:.1%})")


def main():
    """Fonction principale pour tester le système"""

    print("🎯 SYSTÈME DE PRÉDICTION LUPASCO")
    print("=" * 50)

    # Initialiser le système
    systeme = SystemePredictionLupasco()

    # Charger les données
    filename = "exemple.json"  # Fichier de test
    print(f"📂 Chargement des données : {filename}")

    parties = systeme.charger_donnees(filename)
    if not parties:
        print("❌ Aucune donnée chargée")
        return

    print(f"✅ {len(parties)} parties chargées")

    # Test sur une partie spécifique
    if parties:
        print(f"\n🧪 TEST SUR LA PREMIÈRE PARTIE :")
        systeme.tester_prediction_temps_reel(parties[0], affichage_detaille=True)

    # Évaluation sur un échantillon
    if len(parties) > 1:
        print(f"\n📊 ÉVALUATION SUR ÉCHANTILLON :")
        echantillon = parties[:min(100, len(parties))]  # Limiter pour le test
        resultats = systeme.evaluer_predictions(echantillon, debut_evaluation=10)
        systeme.afficher_rapport_evaluation(resultats)


if __name__ == "__main__":
    main()
