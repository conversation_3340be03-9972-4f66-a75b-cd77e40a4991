#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR PROPORTIONS INDEX5 BACCARAT
====================================

Analyse les proportions des 18 valeurs d'INDEX5 dans le dataset baccarat
pour vérifier la distribution et détecter d'éventuels biais.

Les 18 valeurs INDEX5 attendues :
- 0_A_BANKER, 1_A_BANKER
- 0_B_BANKER, 1_B_BANKER  
- 0_C_BANKER, 1_C_BANKER
- 0_A_PLAYER, 1_A_PLAYER
- 0_B_PLAYER, 1_B_PLAYER
- 0_C_PLAYER, 1_C_PLAYER
- 0_A_TIE, 1_A_TIE
- 0_B_TIE, 1_B_TIE
- 0_C_TIE, 1_C_TIE
"""

import json
import os
from collections import Counter, defaultdict
from datetime import datetime
from typing import Dict, List, Tuple

class AnalyseurProportionsIndex5:
    """Analyseur des proportions INDEX5 dans le dataset baccarat"""
    
    def __init__(self):
        """Initialise l'analyseur"""
        # Définir les 18 valeurs INDEX5 attendues
        self.index5_attendues = [
            # BANKER
            "0_A_BANKER", "1_A_BANKER",
            "0_B_BANKER", "1_B_BANKER", 
            "0_C_BANKER", "1_C_BANKER",
            # PLAYER
            "0_A_PLAYER", "1_A_PLAYER",
            "0_B_PLAYER", "1_B_PLAYER",
            "0_C_PLAYER", "1_C_PLAYER", 
            # TIE
            "0_A_TIE", "1_A_TIE",
            "0_B_TIE", "1_B_TIE",
            "0_C_TIE", "1_C_TIE"
        ]
        
        # Compteurs
        self.compteur_index5 = Counter()
        self.total_mains = 0
        self.mains_valides = 0
        self.mains_dummy = 0
        
        # Statistiques par catégorie
        self.stats_par_sync = defaultdict(int)      # 0 vs 1
        self.stats_par_cards = defaultdict(int)     # A vs B vs C
        self.stats_par_result = defaultdict(int)    # PLAYER vs BANKER vs TIE
        
    def charger_dataset(self, filename: str) -> bool:
        """
        Charge et analyse le dataset JSON
        
        Args:
            filename: Nom du fichier JSON à analyser
            
        Returns:
            bool: True si chargement réussi
        """
        if not os.path.exists(filename):
            print(f"❌ Fichier non trouvé : {filename}")
            return False
            
        print(f"📂 Chargement du dataset : {filename}")
        
        try:
            # Détecter la taille du fichier
            file_size = os.path.getsize(filename)
            file_size_gb = file_size / (1024 * 1024 * 1024)

            print(f"📏 Taille du fichier : {file_size_gb:.2f} GB")

            # NOUVEAU : Chargement haute performance avec cache 8GB
            print("🚀 Chargement haute performance avec cache 8GB RAM")
            return self._charger_donnees_cache_8gb(filename)

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False

    def _charger_donnees_cache_8gb(self, filename: str) -> bool:
        """
        Chargement haute performance avec allocation de 8GB de RAM
        Optimisé pour les très gros fichiers JSON (6-7GB)
        """
        import gc
        import sys

        try:
            print("🧠 Allocation de 8GB de RAM pour le cache...")

            # Forcer le garbage collection avant le chargement
            gc.collect()

            # Lire tout le fichier en une fois avec un buffer optimisé
            print("📖 Lecture complète du fichier en mémoire...")

            with open(filename, 'r', encoding='utf-8', buffering=8*1024*1024) as f:  # Buffer 8MB
                # Lire tout le contenu d'un coup
                print("⚡ Chargement du contenu complet...")
                content = f.read()

            print(f"✅ Fichier chargé en mémoire : {len(content):,} caractères")

            # Détecter et corriger le format si nécessaire
            if content.startswith(',{"partie_number"'):
                print("🔧 Correction du format JSON mal formé...")

                # Le fichier se termine par ]} mais on va avoir ]}]} après correction
                # Il faut enlever les 2 derniers caractères ]}
                if content.endswith(']}'):
                    content = content[:-2]  # Enlever ]}
                    print("🔧 Suppression du ]} final en trop")

                # Enlever la virgule du début et ajouter la structure JSON correcte
                content = '{"parties_condensees": [' + content[1:] + ']}'
                print("✅ Format JSON corrigé")
            elif not content.startswith('{"parties_condensees"'):
                print("❌ Format JSON non reconnu")
                return False

            # Parser le JSON avec optimisations
            print("🔄 Parsing JSON haute performance...")
            import json
            data = json.loads(content)

            # Libérer la mémoire du contenu brut
            del content
            gc.collect()

            # Traiter les données
            if 'parties_condensees' in data:
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (cache 8GB)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")
            else:
                print("❌ Structure JSON invalide après correction")
                return False

            print(f"\n🎲 Analyse de {len(parties):,} parties...")

            # Traitement optimisé avec affichage de progression
            for i, partie in enumerate(parties):
                self._analyser_partie(partie)

                # Affichage du progrès tous les 100,000 parties
                if (i + 1) % 100000 == 0:
                    print(f"🔄 Parties traitées : {i + 1:,}/{len(parties):,}")

            print(f"✅ Dataset chargé avec succès en mode cache 8GB !")
            print(f"   • Total parties traitées : {len(parties):,}")
            print(f"   • Total mains analysées : {self.total_mains:,}")
            print(f"   • Mains valides : {self.mains_valides:,}")
            print(f"   • Mains dummy ignorées : {self.mains_dummy:,}")

            return True

        except MemoryError:
            print("❌ Erreur : Mémoire insuffisante pour le cache 8GB")
            print("💡 Suggestion : Fermer d'autres applications ou augmenter la RAM")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement cache 8GB : {e}")
            return False

    def _charger_donnees_standard(self, filename: str) -> bool:
        """Chargement standard pour les fichiers de taille normale"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Vérifier la structure (compatible avec les deux formats)
            if 'parties' in data:
                # Format ancien
                parties = data['parties']
                print("📊 Format détecté : Standard (avec métadonnées)")

                # Analyser les métadonnées si disponibles
                metadata = data.get('metadata', {})
                print(f"📊 Métadonnées :")
                print(f"   • Générateur : {metadata.get('generateur', 'N/A')}")
                print(f"   • Version : {metadata.get('version', 'N/A')}")
                print(f"   • Date génération : {metadata.get('date_generation', 'N/A')}")
                print(f"   • Nombre parties : {metadata.get('nombre_parties', 'N/A')}")
                print(f"   • Hasard cryptographique : {metadata.get('hasard_cryptographique', 'N/A')}")

            elif 'parties_condensees' in data:
                # Format exemple.json
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (exemple.json)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")

            else:
                print("❌ Structure JSON invalide : ni 'parties' ni 'parties_condensees' trouvées")
                return False
            print(f"\n🎲 Analyse de {len(parties)} parties...")

            for partie in parties:
                self._analyser_partie(partie)

            print(f"✅ Dataset chargé avec succès !")
            print(f"   • Total mains analysées : {self.total_mains}")
            print(f"   • Mains valides : {self.mains_valides}")
            print(f"   • Mains dummy ignorées : {self.mains_dummy}")

            return True

        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False

    def _charger_donnees_streaming(self, filename: str) -> bool:
        """
        Chargement par streaming pour les très gros fichiers JSON
        Traite le fichier par chunks pour éviter les problèmes de mémoire
        """
        try:
            print("🔄 Initialisation du streaming JSON...")

            parties_count = 0
            chunk_size = 100000  # Traiter 100,000 parties à la fois pour plus de vitesse
            current_chunk = []

            with open(filename, 'r', encoding='utf-8') as f:
                # Lire plus de contenu au début pour détecter le format
                content = f.read(200)  # Lire 200 caractères au lieu de 50
                f.seek(0)

                print(f"🔍 Début du fichier : {content[:100]}...")

                if '"parties_condensees"' in content:
                    print("📊 Format détecté : Condensé (streaming)")
                    print("📊 Métadonnées : Format condensé sans métadonnées détaillées")

                    # Parser le JSON par streaming
                    parties_count = self._stream_parse_parties_condensees(f, chunk_size)

                elif '"parties"' in content:
                    print("📊 Format détecté : Standard (streaming)")
                    parties_count = self._stream_parse_parties_standard(f, chunk_size)

                elif content.startswith(',{"partie_number"'):
                    print("📊 Format détecté : Condensé mal formé (commence par virgule)")
                    print("📊 Correction automatique du format...")
                    print("📊 Métadonnées : Format condensé sans métadonnées détaillées")

                    # Parser le JSON mal formé par streaming
                    parties_count = self._stream_parse_parties_condensees_malformed(f, chunk_size)

                else:
                    print(f"❌ Format JSON non reconnu pour le streaming")
                    print(f"🔍 Contenu détecté : {content}")
                    return False

            print(f"\n✅ Dataset chargé avec succès en mode streaming !")
            print(f"   • Total parties traitées : {parties_count:,}")
            print(f"   • Total mains analysées : {self.total_mains:,}")
            print(f"   • Mains valides : {self.mains_valides:,}")
            print(f"   • Mains dummy ignorées : {self.mains_dummy:,}")

            return True

        except Exception as e:
            print(f"❌ Erreur lors du streaming : {e}")
            return False

    def _stream_parse_parties_condensees(self, file_handle, chunk_size: int) -> int:
        """
        Parse un fichier JSON avec parties_condensees par streaming
        Retourne le nombre de parties traitées
        """
        import re

        parties_count = 0
        buffer = ""
        partie_pattern = r'"partie_number":\s*(\d+)'

        print("🔄 Début du parsing par streaming...")

        # Lire le fichier par chunks plus gros pour plus de vitesse
        while True:
            chunk = file_handle.read(65536)  # Lire 64KB à la fois
            if not chunk:
                break

            buffer += chunk

            # Chercher les parties complètes dans le buffer
            while True:
                # Trouver le début d'une partie
                start_match = re.search(r'\{"partie_number":', buffer)
                if not start_match:
                    break

                start_pos = start_match.start()

                # Trouver la fin de cette partie (début de la suivante ou fin du tableau)
                next_start = re.search(r'\},\s*\{"partie_number":', buffer[start_pos + 1:])
                if next_start:
                    end_pos = start_pos + 1 + next_start.start() + 1  # +1 pour inclure la }
                else:
                    # Chercher la fin du tableau
                    end_match = re.search(r'\}\s*\]\s*\}', buffer[start_pos:])
                    if end_match:
                        end_pos = start_pos + end_match.start() + 1
                    else:
                        # Partie incomplète, garder dans le buffer
                        break

                # Extraire et parser cette partie
                partie_json = buffer[start_pos:end_pos]
                try:
                    partie = json.loads(partie_json)
                    self._analyser_partie(partie)
                    parties_count += 1

                    # Affichage du progrès tous les 100000 parties
                    if parties_count % 100000 == 0:
                        print(f"🔄 Parties traitées : {parties_count:,}")

                except json.JSONDecodeError:
                    # Ignorer les parties mal formées
                    pass

                # Retirer cette partie du buffer
                buffer = buffer[end_pos:]

        return parties_count

    def _stream_parse_parties_condensees_malformed(self, file_handle, chunk_size: int) -> int:
        """
        Parse un fichier JSON mal formé qui commence par une virgule
        (problème de génération par batch)
        """
        import re

        parties_count = 0
        buffer = ""

        print("🔄 Début du parsing par streaming (fichier mal formé)...")

        # Lire le fichier par chunks plus gros pour plus de vitesse
        while True:
            chunk = file_handle.read(65536)  # Lire 64KB à la fois
            if not chunk:
                break

            buffer += chunk

            # Chercher les parties complètes dans le buffer
            while True:
                # Trouver le début d'une partie (peut commencer par , ou {)
                start_match = re.search(r'[,\s]*\{"partie_number":', buffer)
                if not start_match:
                    break

                start_pos = start_match.start()
                # Ajuster pour commencer à la {
                brace_pos = buffer.find('{"partie_number":', start_pos)
                if brace_pos == -1:
                    break
                start_pos = brace_pos

                # Trouver la fin de cette partie
                brace_count = 0
                end_pos = start_pos
                in_string = False
                escape_next = False

                for i, char in enumerate(buffer[start_pos:], start_pos):
                    if escape_next:
                        escape_next = False
                        continue
                    if char == '\\':
                        escape_next = True
                        continue
                    if char == '"' and not escape_next:
                        in_string = not in_string
                        continue
                    if not in_string:
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_pos = i + 1
                                break

                if brace_count != 0:
                    # Partie incomplète, garder dans le buffer
                    break

                # Extraire et parser cette partie
                partie_json = buffer[start_pos:end_pos]
                try:
                    partie = json.loads(partie_json)
                    self._analyser_partie(partie)
                    parties_count += 1

                    # Affichage du progrès tous les 100000 parties
                    if parties_count % 100000 == 0:
                        print(f"🔄 Parties traitées : {parties_count:,}")

                except json.JSONDecodeError as e:
                    # Ignorer les parties mal formées
                    print(f"⚠️ Partie mal formée ignorée : {e}")

                # Retirer cette partie du buffer
                buffer = buffer[end_pos:]

        return parties_count

    def _stream_parse_parties_standard(self, file_handle, chunk_size: int) -> int:
        """
        Parse un fichier JSON avec parties standard par streaming
        Retourne le nombre de parties traitées
        """
        # Implémentation similaire pour le format standard
        # Pour l'instant, on utilise la méthode standard
        print("⚠️ Streaming pour format standard pas encore implémenté, utilisation standard")
        file_handle.seek(0)
        data = json.load(file_handle)
        parties = data.get('parties', [])

        for partie in parties:
            self._analyser_partie(partie)

        return len(parties)

    def _analyser_partie(self, partie: Dict):
        """Analyse une partie spécifique (compatible avec les deux formats)"""
        # Détecter le format et récupérer les mains
        if 'mains' in partie:
            # Format standard
            mains = partie.get('mains', [])
        elif 'mains_condensees' in partie:
            # Format exemple.json
            mains = partie.get('mains_condensees', [])
        else:
            print("⚠️ Aucune structure de mains trouvée dans la partie")
            return

        for main in mains:
            self.total_mains += 1

            # CORRECTION POUR COMPATIBILITÉ AVEC _append_batch_to_json()
            # Ignorer les mains dummy avec détection améliorée
            if self._est_main_dummy(main):
                self.mains_dummy += 1
                continue

            # Récupérer l'INDEX5
            index5 = main.get('index5', '')

            if index5 == '':
                continue  # Ignorer les mains sans INDEX5

            self.mains_valides += 1

            # Compter l'INDEX5
            self.compteur_index5[index5] += 1

            # Analyser les composants
            self._analyser_composants_index5(index5)

    def _est_main_dummy(self, main):
        """
        Détecte si une main est dummy avec compatibilité pour _append_batch_to_json()

        Cette méthode gère les deux cas :
        1. Format exemple.json : main dummy avec index vides ("")
        2. Format _append_batch_to_json() : main dummy avec vraies valeurs d'index
        """
        main_number = main.get('main_number')
        manche_pb_number = main.get('manche_pb_number')
        index2 = main.get('index2', '')
        index3 = main.get('index3', '')
        index5 = main.get('index5', '')

        # Cas 1: Format exemple.json - main_number = null
        if main_number is None:
            return True

        # Cas 2: Format _append_batch_to_json() - main_number = 0 (main dummy)
        if main_number == 0:
            return True

        # Cas 3: Détection par manche_pb_number = 0 (main dummy)
        if manche_pb_number == 0:
            return True

        # Cas 4: Détection par contenu des index (main dummy générée par Generateur60mains.py)
        if index3 == "DUMMY":
            return True

        if index2 == "dummy":
            return True

        if "_dummy_DUMMY" in index5:
            return True

        return False

    def _analyser_composants_index5(self, index5: str):
        """Analyse les composants d'un INDEX5"""
        try:
            parts = index5.split('_')
            if len(parts) == 3:
                sync, cards, result = parts
                
                # Compter par catégorie
                self.stats_par_sync[sync] += 1
                self.stats_par_cards[cards] += 1
                self.stats_par_result[result] += 1
                
        except Exception:
            pass  # Ignorer les INDEX5 malformés
    
    def generer_rapport(self, filename_output: str = None):
        """
        Génère un rapport détaillé des proportions INDEX5
        
        Args:
            filename_output: Nom du fichier de sortie (optionnel)
        """
        if filename_output is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename_output = f"rapport_proportions_index5_{timestamp}.txt"
        
        print(f"\n📊 Génération du rapport : {filename_output}")
        
        with open(filename_output, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("RAPPORT PROPORTIONS INDEX5 BACCARAT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total mains analysées : {self.total_mains:,}\n")
            f.write(f"Mains valides : {self.mains_valides:,}\n")
            f.write(f"Mains dummy ignorées : {self.mains_dummy:,}\n\n")
            
            # Vérifier les 18 valeurs attendues
            f.write("VÉRIFICATION DES 18 VALEURS INDEX5 ATTENDUES\n")
            f.write("=" * 50 + "\n")
            
            valeurs_trouvees = set(self.compteur_index5.keys())
            valeurs_attendues = set(self.index5_attendues)
            
            valeurs_manquantes = valeurs_attendues - valeurs_trouvees
            valeurs_inattendues = valeurs_trouvees - valeurs_attendues
            
            f.write(f"✅ Valeurs trouvées : {len(valeurs_trouvees)}/18\n")
            f.write(f"❌ Valeurs manquantes : {len(valeurs_manquantes)}\n")
            f.write(f"⚠️  Valeurs inattendues : {len(valeurs_inattendues)}\n\n")
            
            if valeurs_manquantes:
                f.write("VALEURS MANQUANTES :\n")
                for valeur in sorted(valeurs_manquantes):
                    f.write(f"   • {valeur}\n")
                f.write("\n")
            
            if valeurs_inattendues:
                f.write("VALEURS INATTENDUES :\n")
                for valeur in sorted(valeurs_inattendues):
                    count = self.compteur_index5[valeur]
                    pct = (count / self.mains_valides) * 100
                    f.write(f"   • {valeur} : {count:,} ({pct:.3f}%)\n")
                f.write("\n")
            
            # Proportions détaillées des 18 valeurs
            f.write("PROPORTIONS DÉTAILLÉES DES 18 VALEURS INDEX5\n")
            f.write("=" * 50 + "\n")
            f.write("Valeur INDEX5        | Count      | Pourcentage\n")
            f.write("-" * 45 + "\n")

            for index5 in self.index5_attendues:
                count = self.compteur_index5.get(index5, 0)
                pct_observe = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0

                f.write(f"{index5:20s} | {count:10,d} | {pct_observe:9.3f}%\n")
            
            f.write("\n")
            
            # Analyse par composants
            self._ecrire_analyse_composants(f)
            
            # Classification en 3 groupes (original)
            self._ecrire_classification_groupes(f)

            # NOUVELLE CLASSIFICATION EN 6 CLASSES SYNC/RESULT
            self._ecrire_classification_6_classes(f)

            # CLASSIFICATION HIÉRARCHIQUE COMPLÈTE
            self._ecrire_classification_hierarchique(f)
        
        print(f"✅ Rapport généré : {filename_output}")
        return filename_output



    def _ecrire_analyse_composants(self, f):
        """Écrit l'analyse par composants"""
        f.write("ANALYSE PAR COMPOSANTS\n")
        f.write("=" * 30 + "\n")

        # INDEX1 (SYNC)
        f.write("INDEX1 (SYNC/DESYNC) :\n")
        total_sync = sum(self.stats_par_sync.values())
        for sync, count in sorted(self.stats_par_sync.items()):
            pct = (count / total_sync) * 100 if total_sync > 0 else 0
            f.write(f"   • {sync} : {count:,} ({pct:.2f}%)\n")
        f.write("\n")

        # INDEX2 (CARDS)
        f.write("INDEX2 (NOMBRE CARTES) :\n")
        total_cards = sum(self.stats_par_cards.values())
        for cards, count in sorted(self.stats_par_cards.items()):
            pct = (count / total_cards) * 100 if total_cards > 0 else 0
            cartes_nb = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}.get(cards, cards)
            f.write(f"   • {cards} ({cartes_nb}) : {count:,} ({pct:.2f}%)\n")
        f.write("\n")

        # INDEX3 (RESULT)
        f.write("INDEX3 (RÉSULTAT) :\n")
        total_result = sum(self.stats_par_result.values())
        for result, count in sorted(self.stats_par_result.items()):
            pct = (count / total_result) * 100 if total_result > 0 else 0
            f.write(f"   • {result} : {count:,} ({pct:.2f}%)\n")
        f.write("\n")

    def _ecrire_classification_groupes(self, f):
        """Écrit la classification en 3 groupes"""
        f.write("CLASSIFICATION EN 3 GROUPES\n")
        f.write("=" * 30 + "\n")

        # Groupe 1: TIE (rare)
        groupe_tie = []
        total_tie = 0

        # Groupe 2: A + PLAYER/BANKER (modéré)
        groupe_a_pb = []
        total_a_pb = 0

        # Groupe 3: B/C + PLAYER/BANKER (fréquent)
        groupe_bc_pb = []
        total_bc_pb = 0

        for index5 in self.index5_attendues:
            count = self.compteur_index5.get(index5, 0)

            if "_TIE" in index5:
                groupe_tie.append((index5, count))
                total_tie += count
            elif "_A_" in index5 and ("_PLAYER" in index5 or "_BANKER" in index5):
                groupe_a_pb.append((index5, count))
                total_a_pb += count
            elif ("_B_" in index5 or "_C_" in index5) and ("_PLAYER" in index5 or "_BANKER" in index5):
                groupe_bc_pb.append((index5, count))
                total_bc_pb += count

        # Afficher les groupes
        pct_tie = (total_tie / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_a_pb = (total_a_pb / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_bc_pb = (total_bc_pb / self.mains_valides) * 100 if self.mains_valides > 0 else 0

        f.write(f"GROUPE 1 - TIE (RARE) : {total_tie:,} ({pct_tie:.2f}%)\n")
        for index5, count in groupe_tie:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.3f}%)\n")
        f.write("\n")

        f.write(f"GROUPE 2 - A + PLAYER/BANKER (MODÉRÉ) : {total_a_pb:,} ({pct_a_pb:.2f}%)\n")
        for index5, count in groupe_a_pb:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.3f}%)\n")
        f.write("\n")

        f.write(f"GROUPE 3 - B/C + PLAYER/BANKER (FRÉQUENT) : {total_bc_pb:,} ({pct_bc_pb:.2f}%)\n")
        for index5, count in groupe_bc_pb:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.3f}%)\n")
        f.write("\n")

        # Résumé des 3 classes
        f.write("RÉSUMÉ DES 3 CLASSES :\n")
        f.write("-" * 25 + "\n")
        f.write(f"• CLASSE RARE (TIE)     : {pct_tie:6.2f}% ({total_tie:,} mains)\n")
        f.write(f"• CLASSE MODÉRÉE (A+PB) : {pct_a_pb:6.2f}% ({total_a_pb:,} mains)\n")
        f.write(f"• CLASSE FRÉQUENTE (BC+PB): {pct_bc_pb:6.2f}% ({total_bc_pb:,} mains)\n")
        f.write(f"• TOTAL                 : {pct_tie + pct_a_pb + pct_bc_pb:6.2f}% ({self.mains_valides:,} mains)\n")

    def _ecrire_classification_6_classes(self, f):
        """Écrit la classification en 6 classes SYNC/RESULT"""
        f.write("\n")
        f.write("CLASSIFICATION EN 6 CLASSES SYNC/RESULT\n")
        f.write("=" * 45 + "\n")
        f.write("Regroupement par INDEX1 (SYNC) + INDEX3 (RESULT)\n\n")

        # Définir les 6 classes
        classes = {
            "CLASSE 1 - 0_BANKER (SYNC + BANKER)": [],
            "CLASSE 2 - 1_BANKER (DESYNC + BANKER)": [],
            "CLASSE 3 - 0_PLAYER (SYNC + PLAYER)": [],
            "CLASSE 4 - 1_PLAYER (DESYNC + PLAYER)": [],
            "CLASSE 5 - 0_TIE (SYNC + TIE)": [],
            "CLASSE 6 - 1_TIE (DESYNC + TIE)": []
        }

        # Classer chaque INDEX5
        for index5 in self.index5_attendues:
            count = self.compteur_index5.get(index5, 0)

            if index5.startswith("0_") and index5.endswith("_BANKER"):
                classes["CLASSE 1 - 0_BANKER (SYNC + BANKER)"].append((index5, count))
            elif index5.startswith("1_") and index5.endswith("_BANKER"):
                classes["CLASSE 2 - 1_BANKER (DESYNC + BANKER)"].append((index5, count))
            elif index5.startswith("0_") and index5.endswith("_PLAYER"):
                classes["CLASSE 3 - 0_PLAYER (SYNC + PLAYER)"].append((index5, count))
            elif index5.startswith("1_") and index5.endswith("_PLAYER"):
                classes["CLASSE 4 - 1_PLAYER (DESYNC + PLAYER)"].append((index5, count))
            elif index5.startswith("0_") and index5.endswith("_TIE"):
                classes["CLASSE 5 - 0_TIE (SYNC + TIE)"].append((index5, count))
            elif index5.startswith("1_") and index5.endswith("_TIE"):
                classes["CLASSE 6 - 1_TIE (DESYNC + TIE)"].append((index5, count))

        # Afficher chaque classe
        totaux_classes = []

        for nom_classe, valeurs in classes.items():
            total_classe = sum(count for _, count in valeurs)
            pct_classe = (total_classe / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            totaux_classes.append((nom_classe, total_classe, pct_classe))

            f.write(f"{nom_classe} : {total_classe:,} ({pct_classe:.2f}%)\n")

            # Trier par count décroissant
            valeurs_triees = sorted(valeurs, key=lambda x: x[1], reverse=True)

            for index5, count in valeurs_triees:
                pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                f.write(f"   • {index5} : {count:,} ({pct:.3f}%)\n")
            f.write("\n")

        # Résumé des 6 classes
        f.write("RÉSUMÉ DES 6 CLASSES SYNC/RESULT :\n")
        f.write("-" * 40 + "\n")

        # Trier par pourcentage décroissant
        totaux_classes_tries = sorted(totaux_classes, key=lambda x: x[2], reverse=True)

        total_general = 0
        for nom_classe, total_classe, pct_classe in totaux_classes_tries:
            # Extraire le nom court
            nom_court = nom_classe.split(" - ")[1].split(" (")[0]
            f.write(f"• {nom_court:15s} : {pct_classe:6.2f}% ({total_classe:,} mains)\n")
            total_general += pct_classe

        f.write(f"• {'TOTAL':15s} : {total_general:6.2f}% ({self.mains_valides:,} mains)\n")

        # Analyse comparative SYNC vs DESYNC
        f.write("\nANALYSE SYNC vs DESYNC :\n")
        f.write("-" * 25 + "\n")

        total_sync = sum(count for nom, count, _ in totaux_classes_tries if "0_" in nom)
        total_desync = sum(count for nom, count, _ in totaux_classes_tries if "1_" in nom)

        pct_sync = (total_sync / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_desync = (total_desync / self.mains_valides) * 100 if self.mains_valides > 0 else 0

        f.write(f"• SYNC (0_*)   : {pct_sync:6.2f}% ({total_sync:,} mains)\n")
        f.write(f"• DESYNC (1_*) : {pct_desync:6.2f}% ({total_desync:,} mains)\n")
        f.write(f"• ÉQUILIBRE    : {abs(pct_sync - pct_desync):6.2f}% d'écart\n")

    def _ecrire_classification_hierarchique(self, f):
        """Écrit la classification hiérarchique complète"""
        f.write("\n")
        f.write("CLASSIFICATION HIÉRARCHIQUE COMPLÈTE\n")
        f.write("=" * 50 + "\n")
        f.write("Structure : NIVEAU 1 (SYNC/DESYNC) → NIVEAU 2 (RESULT) → NIVEAU 3 (CARDS)\n\n")

        # Structure hiérarchique
        hierarchie = {
            "0": {  # SYNC
                "nom": "CLASSE PRINCIPALE 1 : SYNC (0_*)",
                "sous_classes": {
                    "BANKER": {
                        "nom": "SOUS-CLASSE 1.1 : 0_BANKER",
                        "valeurs": ["0_A_BANKER", "0_B_BANKER", "0_C_BANKER"]
                    },
                    "PLAYER": {
                        "nom": "SOUS-CLASSE 1.2 : 0_PLAYER",
                        "valeurs": ["0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER"]
                    },
                    "TIE": {
                        "nom": "SOUS-CLASSE 1.3 : 0_TIE",
                        "valeurs": ["0_A_TIE", "0_B_TIE", "0_C_TIE"]
                    }
                }
            },
            "1": {  # DESYNC
                "nom": "CLASSE PRINCIPALE 2 : DESYNC (1_*)",
                "sous_classes": {
                    "BANKER": {
                        "nom": "SOUS-CLASSE 2.1 : 1_BANKER",
                        "valeurs": ["1_A_BANKER", "1_B_BANKER", "1_C_BANKER"]
                    },
                    "PLAYER": {
                        "nom": "SOUS-CLASSE 2.2 : 1_PLAYER",
                        "valeurs": ["1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER"]
                    },
                    "TIE": {
                        "nom": "SOUS-CLASSE 2.3 : 1_TIE",
                        "valeurs": ["1_A_TIE", "1_B_TIE", "1_C_TIE"]
                    }
                }
            }
        }

        # Analyser chaque niveau hiérarchique
        for sync_key, sync_data in hierarchie.items():
            # NIVEAU 1 : Classe principale
            total_niveau1 = sum(self.compteur_index5.get(index5, 0)
                              for sous_classe in sync_data["sous_classes"].values()
                              for index5 in sous_classe["valeurs"])

            pct_niveau1 = (total_niveau1 / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            f.write(f"🔴 {sync_data['nom']} : {total_niveau1:,} ({pct_niveau1:.2f}%)\n")

            # NIVEAU 2 : Sous-classes
            for result_key, result_data in sync_data["sous_classes"].items():
                total_niveau2 = sum(self.compteur_index5.get(index5, 0) for index5 in result_data["valeurs"])
                pct_niveau2 = (total_niveau2 / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                pct_relatif_niveau2 = (total_niveau2 / total_niveau1) * 100 if total_niveau1 > 0 else 0

                f.write(f"├── {result_data['nom']} : {total_niveau2:,} ({pct_niveau2:.2f}% | {pct_relatif_niveau2:.1f}% du parent)\n")

                # NIVEAU 3 : Valeurs finales (triées par count décroissant)
                valeurs_avec_counts = [(index5, self.compteur_index5.get(index5, 0)) for index5 in result_data["valeurs"]]
                valeurs_triees = sorted(valeurs_avec_counts, key=lambda x: x[1], reverse=True)

                for i, (index5, count) in enumerate(valeurs_triees):
                    pct_niveau3 = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                    pct_relatif_niveau3 = (count / total_niveau2) * 100 if total_niveau2 > 0 else 0

                    # Symbole pour le dernier élément
                    symbole = "└──" if i == len(valeurs_triees) - 1 else "├──"

                    f.write(f"│   {symbole} {index5} : {count:,} ({pct_niveau3:.3f}% | {pct_relatif_niveau3:.1f}% du parent)\n")

                f.write("│\n")

            f.write("\n")

        # Résumé des niveaux
        self._ecrire_resume_hierarchique(f, hierarchie)

        # ANALYSE DES SOUS-SOUS-CLASSES A, B, C
        self._ecrire_analyse_sous_sous_classes(f, hierarchie)

    def _ecrire_resume_hierarchique(self, f, hierarchie):
        """Écrit le résumé de l'analyse hiérarchique"""
        f.write("RÉSUMÉ HIÉRARCHIQUE\n")
        f.write("=" * 25 + "\n")

        # NIVEAU 1 : Comparaison SYNC vs DESYNC
        f.write("NIVEAU 1 - CLASSES PRINCIPALES :\n")
        f.write("-" * 35 + "\n")

        for sync_key, sync_data in hierarchie.items():
            total_niveau1 = sum(self.compteur_index5.get(index5, 0)
                              for sous_classe in sync_data["sous_classes"].values()
                              for index5 in sous_classe["valeurs"])
            pct_niveau1 = (total_niveau1 / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            nom_court = "SYNC" if sync_key == "0" else "DESYNC"
            f.write(f"• {nom_court:6s} ({sync_key}_*) : {pct_niveau1:6.2f}% ({total_niveau1:,} mains)\n")

        # NIVEAU 2 : Comparaison par résultat
        f.write("\nNIVEAU 2 - SOUS-CLASSES PAR RÉSULTAT :\n")
        f.write("-" * 40 + "\n")

        # Regrouper par résultat
        resultats_totaux = {}
        for sync_key, sync_data in hierarchie.items():
            for result_key, result_data in sync_data["sous_classes"].items():
                if result_key not in resultats_totaux:
                    resultats_totaux[result_key] = {}

                total = sum(self.compteur_index5.get(index5, 0) for index5 in result_data["valeurs"])
                resultats_totaux[result_key][sync_key] = total

        for result_key in ["BANKER", "PLAYER", "TIE"]:
            f.write(f"• {result_key} :\n")
            total_result = sum(resultats_totaux[result_key].values())
            pct_total = (total_result / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            for sync_key in ["0", "1"]:
                count = resultats_totaux[result_key].get(sync_key, 0)
                pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                pct_relatif = (count / total_result) * 100 if total_result > 0 else 0
                sync_nom = "SYNC" if sync_key == "0" else "DESYNC"

                f.write(f"  - {sync_nom:6s} : {pct:6.2f}% ({pct_relatif:5.1f}% du {result_key})\n")

            f.write(f"  - TOTAL {result_key:6s} : {pct_total:6.2f}% ({total_result:,} mains)\n")
            f.write("\n")

    def _ecrire_analyse_sous_sous_classes(self, f, hierarchie):
        """Écrit l'analyse détaillée des sous-sous-classes A, B, C"""
        f.write("\nANALYSE DES SOUS-SOUS-CLASSES A, B, C\n")
        f.write("=" * 45 + "\n")
        f.write("Analyse transversale des catégories de cartes à travers toute la hiérarchie\n\n")

        # Collecter toutes les données par catégorie A, B, C
        categories_data = {
            'A': {'total': 0, 'details': []},
            'B': {'total': 0, 'details': []},
            'C': {'total': 0, 'details': []}
        }

        # Parcourir toute la hiérarchie pour collecter les données
        for sync_key, sync_data in hierarchie.items():
            sync_nom = "SYNC" if sync_key == "0" else "DESYNC"

            for result_key, result_data in sync_data["sous_classes"].items():
                for index5 in result_data["valeurs"]:
                    # Extraire la catégorie (A, B, ou C)
                    parts = index5.split('_')
                    if len(parts) >= 2:
                        categorie = parts[1]  # A, B, ou C
                        count = self.compteur_index5.get(index5, 0)

                        if categorie in categories_data:
                            categories_data[categorie]['total'] += count
                            categories_data[categorie]['details'].append({
                                'index5': index5,
                                'count': count,
                                'sync': sync_nom,
                                'result': result_key
                            })

        # Afficher l'analyse par catégorie
        f.write("NIVEAU 3 - ANALYSE PAR CATÉGORIE DE CARTES :\n")
        f.write("-" * 45 + "\n")

        # Trier les catégories par total décroissant
        categories_triees = sorted(categories_data.items(), key=lambda x: x[1]['total'], reverse=True)

        for categorie, data in categories_triees:
            total_cat = data['total']
            pct_cat = (total_cat / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            # Description de la catégorie
            desc_cartes = {"A": "4 cartes (Naturels)", "B": "6 cartes (Double tirage)", "C": "5 cartes (Tirage simple)"}
            description = desc_cartes.get(categorie, categorie)

            f.write(f"🔸 CATÉGORIE {categorie} - {description} : {total_cat:,} ({pct_cat:.2f}%)\n")

            # Regrouper par SYNC/DESYNC
            sync_totaux = {'SYNC': 0, 'DESYNC': 0}
            result_totaux = {'BANKER': 0, 'PLAYER': 0, 'TIE': 0}

            for detail in data['details']:
                sync_totaux[detail['sync']] += detail['count']
                result_totaux[detail['result']] += detail['count']

            # Afficher répartition SYNC/DESYNC
            for sync_type, count in sync_totaux.items():
                pct = (count / total_cat) * 100 if total_cat > 0 else 0
                f.write(f"  • {sync_type:6s} : {count:,} ({pct:.1f}% de la catégorie {categorie})\n")

            # Afficher répartition par résultat
            f.write(f"  • Répartition par résultat :\n")
            for result_type, count in sorted(result_totaux.items(), key=lambda x: x[1], reverse=True):
                pct = (count / total_cat) * 100 if total_cat > 0 else 0
                f.write(f"    - {result_type:7s} : {count:,} ({pct:.1f}%)\n")

            # Détail des 18 valeurs pour cette catégorie (triées par count)
            details_tries = sorted(data['details'], key=lambda x: x['count'], reverse=True)
            f.write(f"  • Détail des valeurs :\n")
            for detail in details_tries:
                pct_detail = (detail['count'] / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                pct_relatif = (detail['count'] / total_cat) * 100 if total_cat > 0 else 0
                f.write(f"    - {detail['index5']:12s} : {detail['count']:,} ({pct_detail:.3f}% | {pct_relatif:.1f}% de {categorie})\n")

            f.write("\n")

        # Résumé comparatif des 3 catégories
        f.write("RÉSUMÉ COMPARATIF DES CATÉGORIES :\n")
        f.write("-" * 35 + "\n")

        total_general = sum(data['total'] for _, data in categories_data.items())

        for categorie, data in categories_triees:
            total_cat = data['total']
            pct_cat = (total_cat / total_general) * 100 if total_general > 0 else 0
            pct_global = (total_cat / self.mains_valides) * 100 if self.mains_valides > 0 else 0

            desc_cartes = {"A": "Naturels (4)", "B": "Double (6)", "C": "Simple (5)"}
            description = desc_cartes.get(categorie, categorie)

            f.write(f"• {categorie} ({description:12s}) : {pct_global:6.2f}% ({total_cat:,} mains)\n")

        f.write(f"• TOTAL VÉRIFIÉ        : {(total_general/self.mains_valides)*100:6.2f}% ({total_general:,} mains)\n")

        # Analyse des équilibres SYNC/DESYNC par catégorie
        f.write("\nÉQUILIBRE SYNC/DESYNC PAR CATÉGORIE :\n")
        f.write("-" * 40 + "\n")

        for categorie, data in categories_triees:
            sync_count = sum(d['count'] for d in data['details'] if d['sync'] == 'SYNC')
            desync_count = sum(d['count'] for d in data['details'] if d['sync'] == 'DESYNC')
            total_cat = sync_count + desync_count

            if total_cat > 0:
                pct_sync = (sync_count / total_cat) * 100
                pct_desync = (desync_count / total_cat) * 100
                ecart = abs(pct_sync - pct_desync)

                f.write(f"• Catégorie {categorie} :\n")
                f.write(f"  - SYNC   : {pct_sync:5.1f}% ({sync_count:,})\n")
                f.write(f"  - DESYNC : {pct_desync:5.1f}% ({desync_count:,})\n")
                f.write(f"  - ÉCART  : {ecart:5.1f}%\n")
                f.write("\n")


def main():
    """Fonction principale"""
    print("🎯 ANALYSEUR PROPORTIONS INDEX5 BACCARAT")
    print("=" * 50)

    # Nom du fichier dataset (format exemple.json)
    filename = "dataset_baccarat_lupasco_20250703_180708_condensed.json"

    # Créer l'analyseur
    analyseur = AnalyseurProportionsIndex5()

    # Charger et analyser le dataset
    if analyseur.charger_dataset(filename):
        # Générer le rapport
        rapport_file = analyseur.generer_rapport()

        print(f"\n🎉 ANALYSE TERMINÉE !")
        print(f"📊 Rapport détaillé : {rapport_file}")
        print(f"🔍 Consultez le fichier pour voir les proportions exactes des 18 valeurs INDEX5")
    else:
        print("❌ Échec de l'analyse")


if __name__ == "__main__":
    main()
