#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PRÉDICTEUR OPTIMISÉ LUPASCO
==========================

Système de prédiction optimisé basé sur les patterns confirmés
dans l'analyse statistique du dataset de 1M parties

Patterns confirmés :
- C → BANKER : 50.9% (légèrement favorisé)
- B → PLAYER : 49.3% (légèrement défavorisé) 
- A → Équilibré : 50.9% BANKER / 49.1% PLAYER

Auteur: Augment Agent
Date: 2025-07-03
"""

import json
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict, Counter
import time


@dataclass
class MainLupasco:
    """Structure d'une main Lupasco"""
    main_number: Optional[int]
    manche_pb_number: Optional[int]
    index1: int
    index2: str
    index3: str
    index5: str


@dataclass
class PredictionOptimisee:
    """Résultat de prédiction optimisée"""
    prediction: str
    confidence: float
    method_used: str
    index2_predit: str
    probabilites: Dict[str, float]
    patterns_detectes: List[str]


class PredicteurOptimiseLupasco:
    """Prédicteur optimisé basé sur les patterns confirmés"""
    
    def __init__(self):
        # Probabilités confirmées par l'analyse statistique
        self.probas_confirmees = {
            'A': {'BANKER': 0.509, 'PLAYER': 0.491},
            'B': {'BANKER': 0.507, 'PLAYER': 0.493},  # Inverse de B→PLAYER
            'C': {'BANKER': 0.509, 'PLAYER': 0.491}
        }
        
        # Règles de transition INDEX1 (déterministes)
        self.transitions_index1 = {
            ('A', 0): 0, ('A', 1): 1,  # A conserve
            ('B', 0): 0, ('B', 1): 1,  # B conserve  
            ('C', 0): 1, ('C', 1): 0   # C bascule
        }
        
        # Patterns de séquences INDEX2
        self.patterns_sequences = {
            'CCC': {'BANKER': 0.52, 'PLAYER': 0.48},
            'BBB': {'BANKER': 0.48, 'PLAYER': 0.52},
            'AAA': {'BANKER': 0.50, 'PLAYER': 0.50}
        }
    
    def extraire_mains_valides(self, partie: Dict) -> List[MainLupasco]:
        """Extrait les mains valides (exclut dummy)"""
        mains = []
        for main_data in partie['mains_condensees']:
            if main_data['main_number'] is not None:
                main = MainLupasco(
                    main_number=main_data['main_number'],
                    manche_pb_number=main_data['manche_pb_number'],
                    index1=main_data['index1'],
                    index2=main_data['index2'],
                    index3=main_data['index3'],
                    index5=main_data['index5']
                )
                mains.append(main)
        return mains
    
    def predire_index2_suivant(self, mains: List[MainLupasco]) -> Tuple[str, float]:
        """Prédit INDEX2 suivant basé sur les règles Lupasco"""
        if not mains:
            return 'A', 0.33
        
        derniere_main = mains[-1]
        index1_actuel = derniere_main.index1
        index2_actuel = derniere_main.index2
        
        # Calculer INDEX1 suivant (déterministe)
        index1_suivant = self.transitions_index1.get((index2_actuel, index1_actuel), index1_actuel)
        
        # Analyser les fréquences récentes d'INDEX2 pour équilibrage
        sequences_recentes = [m.index2 for m in mains[-15:]]  # 15 dernières mains
        counter = Counter(sequences_recentes)
        
        # Calculer les probabilités d'équilibrage
        total = sum(counter.values())
        if total > 0:
            freq_A = counter.get('A', 0) / total
            freq_B = counter.get('B', 0) / total
            freq_C = counter.get('C', 0) / total
            
            # Favoriser l'INDEX2 le moins fréquent (mécanisme d'équilibrage)
            probas_equilibrage = {
                'A': 1 - freq_A,
                'B': 1 - freq_B,
                'C': 1 - freq_C
            }
            
            # Normaliser
            total_prob = sum(probas_equilibrage.values())
            for key in probas_equilibrage:
                probas_equilibrage[key] /= total_prob
            
            # Prédiction basée sur l'équilibrage
            index2_predit = max(probas_equilibrage, key=probas_equilibrage.get)
            confidence = probas_equilibrage[index2_predit]
            
            return index2_predit, confidence
        
        # Fallback : distribution uniforme
        return 'A', 0.33
    
    def analyser_sequences_index2(self, mains: List[MainLupasco]) -> Dict[str, Any]:
        """Analyse les séquences d'INDEX2 pour patterns spéciaux"""
        if len(mains) < 3:
            return {'sequences_detectees': [], 'pattern_dominant': None}
        
        # Analyser les 3 dernières mains
        derniers_index2 = [m.index2 for m in mains[-3:]]
        sequence_3 = ''.join(derniers_index2)
        
        # Analyser les 5 dernières mains si disponibles
        sequences_detectees = [sequence_3]
        if len(mains) >= 5:
            derniers_index2_5 = [m.index2 for m in mains[-5:]]
            sequence_5 = ''.join(derniers_index2_5)
            sequences_detectees.append(sequence_5)
        
        # Détecter le pattern dominant
        pattern_dominant = None
        if sequence_3 in self.patterns_sequences:
            pattern_dominant = sequence_3
        
        return {
            'sequences_detectees': sequences_detectees,
            'pattern_dominant': pattern_dominant,
            'sequence_3': sequence_3
        }
    
    def analyser_regulation_banker_player(self, mains: List[MainLupasco]) -> Dict[str, Any]:
        """Analyse les mécanismes de régulation BANKER/PLAYER"""
        if len(mains) < 10:
            return {'regulation_active': False, 'tendance_correction': None}
        
        # Compter BANKER/PLAYER dans toute la partie
        banker_total = sum(1 for m in mains if m.index3 == 'BANKER')
        player_total = sum(1 for m in mains if m.index3 == 'PLAYER')
        total_bp = banker_total + player_total
        
        if total_bp == 0:
            return {'regulation_active': False, 'tendance_correction': None}
        
        # Analyser les 10 dernières mains
        mains_recentes = [m for m in mains[-10:] if m.index3 in ['BANKER', 'PLAYER']]
        banker_recent = sum(1 for m in mains_recentes if m.index3 == 'BANKER')
        player_recent = sum(1 for m in mains_recentes if m.index3 == 'PLAYER')
        
        # Détecter la régulation
        desequilibre_global = abs(banker_total - player_total) / total_bp
        regulation_active = False
        tendance_correction = None
        
        if desequilibre_global > 0.1:  # Seuil de déséquilibre significatif
            if banker_total > player_total and player_recent > banker_recent:
                regulation_active = True
                tendance_correction = 'PLAYER'
            elif player_total > banker_total and banker_recent > player_recent:
                regulation_active = True
                tendance_correction = 'BANKER'
        
        return {
            'regulation_active': regulation_active,
            'tendance_correction': tendance_correction,
            'desequilibre_global': desequilibre_global,
            'banker_total': banker_total,
            'player_total': player_total,
            'banker_recent': banker_recent,
            'player_recent': player_recent
        }
    
    def predire_index3_optimise(self, mains: List[MainLupasco]) -> PredictionOptimisee:
        """Prédiction optimisée d'INDEX3"""
        
        if not mains:
            return PredictionOptimisee(
                prediction='BANKER',
                confidence=0.5,
                method_used='default',
                index2_predit='A',
                probabilites={'BANKER': 0.5, 'PLAYER': 0.5},
                patterns_detectes=['no_history']
            )
        
        # 1. Prédire INDEX2 suivant
        index2_predit, conf_index2 = self.predire_index2_suivant(mains)
        
        # 2. Analyser les séquences
        sequences = self.analyser_sequences_index2(mains)
        
        # 3. Analyser la régulation
        regulation = self.analyser_regulation_banker_player(mains)
        
        # 4. Probabilités de base selon INDEX2 prédit
        probas_base = self.probas_confirmees[index2_predit].copy()
        
        # 5. Ajustements selon les patterns détectés
        patterns_detectes = [f'index2_predit_{index2_predit}']
        
        # Ajustement pour séquences spéciales
        if sequences['pattern_dominant']:
            pattern = sequences['pattern_dominant']
            probas_pattern = self.patterns_sequences[pattern]
            # Fusion pondérée
            for outcome in ['BANKER', 'PLAYER']:
                probas_base[outcome] = 0.7 * probas_base[outcome] + 0.3 * probas_pattern[outcome]
            patterns_detectes.append(f'sequence_{pattern}')
        
        # Ajustement pour régulation
        if regulation['regulation_active']:
            tendance = regulation['tendance_correction']
            if tendance == 'BANKER':
                probas_base['BANKER'] *= 1.15
                probas_base['PLAYER'] *= 0.85
            elif tendance == 'PLAYER':
                probas_base['PLAYER'] *= 1.15
                probas_base['BANKER'] *= 0.85
            patterns_detectes.append(f'regulation_{tendance}')
        
        # 6. Normaliser les probabilités
        total_prob = sum(probas_base.values())
        for outcome in probas_base:
            probas_base[outcome] /= total_prob
        
        # 7. Prédiction finale
        prediction = max(probas_base, key=probas_base.get)
        confidence = max(probas_base.values())
        
        # 8. Déterminer la méthode utilisée
        method_used = 'optimise'
        if sequences['pattern_dominant']:
            method_used += '_sequence'
        if regulation['regulation_active']:
            method_used += '_regulation'
        
        return PredictionOptimisee(
            prediction=prediction,
            confidence=confidence,
            method_used=method_used,
            index2_predit=index2_predit,
            probabilites=probas_base,
            patterns_detectes=patterns_detectes
        )


class SystemePredictionOptimise:
    """Système de prédiction optimisé"""
    
    def __init__(self):
        self.predicteur = PredicteurOptimiseLupasco()
    
    def charger_donnees(self, filename: str, max_parties: int = None) -> List[Dict]:
        """Charge les données"""
        try:
            with open(filename, 'r', encoding='utf-8', buffering=64*1024) as f:
                content = f.read()
            
            # Correction format JSON
            if content.startswith(',{"partie_number"'):
                if content.endswith(']}'):
                    content = content[:-2]
                content = '{"parties_condensees": [' + content[1:] + ']}'
            
            data = json.loads(content)
            parties = data.get('parties_condensees', [])
            
            if max_parties:
                parties = parties[:max_parties]
            
            return parties
        except Exception as e:
            print(f"❌ Erreur chargement : {e}")
            return []
    
    def evaluer_performance(self, parties: List[Dict], max_parties: int = 1000) -> Dict:
        """Évalue la performance du système optimisé"""
        
        print(f"🎯 ÉVALUATION SYSTÈME OPTIMISÉ")
        print(f"📊 Parties à tester : {min(max_parties, len(parties)):,}")
        print("=" * 50)
        
        total_predictions = 0
        predictions_correctes = 0
        stats_par_methode = defaultdict(lambda: {'total': 0, 'correct': 0})
        stats_par_index2 = defaultdict(lambda: {'total': 0, 'correct': 0})
        
        debut = time.time()
        
        for i, partie in enumerate(parties[:max_parties]):
            if i % 200 == 0:
                print(f"🔄 Parties testées : {i:,}")
            
            mains = self.predicteur.extraire_mains_valides(partie)
            
            # Tester les prédictions
            for position in range(10, len(mains), 2):  # Échantillonner
                mains_historique = mains[:position]
                main_reelle = mains[position]
                
                if main_reelle.index3 not in ['BANKER', 'PLAYER']:
                    continue
                
                # Faire la prédiction
                prediction = self.predicteur.predire_index3_optimise(mains_historique)
                
                # Vérifier
                correct = (prediction.prediction == main_reelle.index3)
                
                total_predictions += 1
                if correct:
                    predictions_correctes += 1
                
                # Stats par méthode
                stats_par_methode[prediction.method_used]['total'] += 1
                if correct:
                    stats_par_methode[prediction.method_used]['correct'] += 1
                
                # Stats par INDEX2 prédit
                stats_par_index2[prediction.index2_predit]['total'] += 1
                if correct:
                    stats_par_index2[prediction.index2_predit]['correct'] += 1
        
        duree = time.time() - debut
        taux_global = predictions_correctes / total_predictions if total_predictions > 0 else 0
        
        print(f"\n✅ RÉSULTATS FINAUX :")
        print(f"   Prédictions : {predictions_correctes:,}/{total_predictions:,}")
        print(f"   Taux réussite : {taux_global:.1%}")
        print(f"   Durée : {duree:.1f}s")
        print(f"   Vitesse : {total_predictions/duree:.0f} prédictions/s")
        
        print(f"\n📊 PERFORMANCE PAR INDEX2 PRÉDIT :")
        print("-" * 40)
        for index2, stats in stats_par_index2.items():
            if stats['total'] > 0:
                taux = stats['correct'] / stats['total']
                print(f"   {index2} : {taux:.1%} ({stats['correct']:,}/{stats['total']:,})")
        
        print(f"\n🔧 PERFORMANCE PAR MÉTHODE :")
        print("-" * 40)
        for methode, stats in stats_par_methode.items():
            if stats['total'] > 0:
                taux = stats['correct'] / stats['total']
                print(f"   {methode:20s} : {taux:.1%} ({stats['correct']:,}/{stats['total']:,})")
        
        return {
            'taux_global': taux_global,
            'total_predictions': total_predictions,
            'predictions_correctes': predictions_correctes,
            'stats_par_methode': dict(stats_par_methode),
            'stats_par_index2': dict(stats_par_index2),
            'duree': duree
        }


def main():
    """Test du système optimisé"""
    
    print("🚀 SYSTÈME DE PRÉDICTION OPTIMISÉ LUPASCO")
    print("=" * 60)
    
    systeme = SystemePredictionOptimise()
    
    # Charger données
    filename = "dataset_baccarat_lupasco_20250703_180708_condensed.json"
    parties = systeme.charger_donnees(filename, max_parties=5000)
    
    if not parties:
        print("❌ Pas de données")
        return
    
    print(f"✅ {len(parties):,} parties chargées")
    
    # Évaluer
    resultats = systeme.evaluer_performance(parties, max_parties=3000)


if __name__ == "__main__":
    main()
