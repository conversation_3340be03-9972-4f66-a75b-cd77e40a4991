#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST SIMPLE DU SYSTÈME DE PRÉDICTION LUPASCO
============================================

Test rapide et simple pour valider le système de prédiction

Auteur: Augment Agent
Date: 2025-07-03
"""

import json
import time
from predicteur_lupasco import SystemePredictionLupasco
import gc


def charger_donnees_rapide(filename: str, max_parties: int = 1000):
    """Charge rapidement un échantillon de données"""
    
    print(f"📂 Chargement : {filename}")
    print(f"📊 Limite : {max_parties:,} parties")
    
    try:
        with open(filename, 'r', encoding='utf-8', buffering=64*1024) as f:
            content = f.read()
        
        # Correction du format JSON mal formé
        if content.startswith(',{"partie_number"'):
            print("🔧 Correction du format JSON...")
            if content.endswith(']}'):
                content = content[:-2]
            content = '{"parties_condensees": [' + content[1:] + ']}'
        
        print("🔄 Parsing JSON...")
        data = json.loads(content)
        del content
        gc.collect()
        
        parties = data.get('parties_condensees', [])[:max_parties]
        print(f"✅ {len(parties):,} parties chargées")
        return parties
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return []


def test_une_partie(systeme, partie):
    """Test sur une seule partie"""
    
    mains = systeme.predicteur.analyseur.extraire_mains_valides(partie)
    
    print(f"\n🎮 TEST PARTIE {partie['partie_number']}")
    print(f"📊 {len(mains)} mains dans la partie")
    print("-" * 60)
    print("Main | Historique | Prédiction | Réel     | ✓/✗ | Confiance")
    print("-" * 60)
    
    predictions_correctes = 0
    total_predictions = 0
    
    for position in range(5, min(20, len(mains))):  # Test sur les 15 premières mains
        # Prédire
        prediction = systeme.predire_main_suivante(partie, position)
        main_reelle = mains[position]
        
        # Vérifier
        correct = (prediction.prediction == main_reelle.index3)
        if correct:
            predictions_correctes += 1
        total_predictions += 1
        
        # Afficher
        status = "✅" if correct else "❌"
        historique = ''.join([m.index3[0] for m in mains[max(0, position-3):position]])
        
        print(f"{position:4d} | {historique:10s} | {prediction.prediction:10s} | {main_reelle.index3:8s} | {status} | {prediction.confidence:.1%}")
    
    taux = predictions_correctes / total_predictions if total_predictions > 0 else 0
    print("-" * 60)
    print(f"🎯 Résultat : {predictions_correctes}/{total_predictions} ({taux:.1%})")
    
    return taux


def test_patterns_simples(systeme, parties, max_test=500):
    """Test des patterns simples identifiés"""
    
    print(f"\n🔍 TEST PATTERNS SIMPLES")
    print(f"📊 {min(max_test, len(parties)):,} parties analysées")
    print("=" * 50)
    
    # Compteurs pour les patterns
    patterns = {
        'C_to_BANKER': {'total': 0, 'correct': 0},
        'B_to_PLAYER': {'total': 0, 'correct': 0},
        'A_equilibre': {'total': 0, 'banker': 0, 'player': 0}
    }
    
    for i, partie in enumerate(parties[:max_test]):
        if i % 100 == 0:
            print(f"🔄 Parties analysées : {i:,}")
        
        mains = systeme.predicteur.analyseur.extraire_mains_valides(partie)
        
        for position in range(1, len(mains)):
            main_precedente = mains[position - 1]
            main_actuelle = mains[position]
            
            if main_actuelle.index3 not in ['BANKER', 'PLAYER']:
                continue
            
            # Pattern C → BANKER
            if main_precedente.index2 == 'C':
                patterns['C_to_BANKER']['total'] += 1
                if main_actuelle.index3 == 'BANKER':
                    patterns['C_to_BANKER']['correct'] += 1
            
            # Pattern B → PLAYER
            elif main_precedente.index2 == 'B':
                patterns['B_to_PLAYER']['total'] += 1
                if main_actuelle.index3 == 'PLAYER':
                    patterns['B_to_PLAYER']['correct'] += 1
            
            # Pattern A → équilibre
            elif main_precedente.index2 == 'A':
                patterns['A_equilibre']['total'] += 1
                if main_actuelle.index3 == 'BANKER':
                    patterns['A_equilibre']['banker'] += 1
                else:
                    patterns['A_equilibre']['player'] += 1
    
    # Afficher les résultats
    print(f"\n📊 RÉSULTATS PATTERNS :")
    print("-" * 40)
    
    # Pattern C → BANKER
    if patterns['C_to_BANKER']['total'] > 0:
        taux_c = patterns['C_to_BANKER']['correct'] / patterns['C_to_BANKER']['total']
        print(f"C → BANKER : {taux_c:.1%} ({patterns['C_to_BANKER']['correct']:,}/{patterns['C_to_BANKER']['total']:,})")
    
    # Pattern B → PLAYER
    if patterns['B_to_PLAYER']['total'] > 0:
        taux_b = patterns['B_to_PLAYER']['correct'] / patterns['B_to_PLAYER']['total']
        print(f"B → PLAYER : {taux_b:.1%} ({patterns['B_to_PLAYER']['correct']:,}/{patterns['B_to_PLAYER']['total']:,})")
    
    # Pattern A → équilibre
    if patterns['A_equilibre']['total'] > 0:
        banker_pct = patterns['A_equilibre']['banker'] / patterns['A_equilibre']['total']
        player_pct = patterns['A_equilibre']['player'] / patterns['A_equilibre']['total']
        print(f"A → BANKER : {banker_pct:.1%} ({patterns['A_equilibre']['banker']:,}/{patterns['A_equilibre']['total']:,})")
        print(f"A → PLAYER : {player_pct:.1%} ({patterns['A_equilibre']['player']:,}/{patterns['A_equilibre']['total']:,})")
    
    return patterns


def test_echantillon_rapide(systeme, parties, taille_echantillon=200):
    """Test rapide sur un échantillon"""
    
    print(f"\n🚀 TEST ÉCHANTILLON RAPIDE")
    print(f"📊 {taille_echantillon:,} parties")
    print("=" * 40)
    
    total_predictions = 0
    predictions_correctes = 0
    
    debut = time.time()
    
    for i, partie in enumerate(parties[:taille_echantillon]):
        if i % 50 == 0:
            print(f"🔄 Parties testées : {i:,}")
        
        mains = systeme.predicteur.analyseur.extraire_mains_valides(partie)
        
        # Tester quelques prédictions par partie
        for position in range(10, min(25, len(mains)), 3):  # Échantillonner
            prediction = systeme.predire_main_suivante(partie, position)
            main_reelle = mains[position]
            
            if main_reelle.index3 in ['BANKER', 'PLAYER']:
                total_predictions += 1
                if prediction.prediction == main_reelle.index3:
                    predictions_correctes += 1
    
    duree = time.time() - debut
    taux = predictions_correctes / total_predictions if total_predictions > 0 else 0
    
    print(f"\n✅ RÉSULTATS :")
    print(f"   Prédictions : {predictions_correctes:,}/{total_predictions:,}")
    print(f"   Taux réussite : {taux:.1%}")
    print(f"   Durée : {duree:.1f}s")
    print(f"   Vitesse : {total_predictions/duree:.0f} prédictions/s")
    
    return taux


def main():
    """Test principal"""
    
    print("🎯 TEST SIMPLE SYSTÈME LUPASCO")
    print("=" * 50)
    
    # Initialiser
    systeme = SystemePredictionLupasco()
    
    # Charger données
    filename = "dataset_baccarat_lupasco_20250703_190003_condensed.json"
    parties = charger_donnees_rapide(filename, max_parties=10000)
    
    if not parties:
        print("❌ Pas de données")
        return
    
    try:
        # Test 1 : Une partie détaillée
        if parties:
            test_une_partie(systeme, parties[0])
        
        # Test 2 : Patterns simples (échantillon plus large)
        test_patterns_simples(systeme, parties, max_test=5000)

        # Test 3 : Échantillon rapide (plus large)
        test_echantillon_rapide(systeme, parties, taille_echantillon=2000)
        
        print(f"\n✅ TESTS TERMINÉS")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Interrompu")
    except Exception as e:
        print(f"\n❌ Erreur : {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
