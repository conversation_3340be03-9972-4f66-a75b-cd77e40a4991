import numpy as np
import pandas as pd
from scipy import stats
from collections import defaultdict
import math

class IndiceCoherenceFractale:
    def __init__(self):
        self.proportions_reference = {
            'SYNC': 0.4968,
            'DESYNC': 0.5032,
            'A': 0.3790,
            'B': 0.3177,
            'C': 0.3033,
            'BANKER': 0.4586,
            'PLAYER': 0.4463,
            'TIE': 0.0951
        }
    
    def calculer_icf(self, sequence_index5, fenetre=1000):
        """
        Calcule l'Indice de Cohérence Fractale sur une séquence
        """
        icf_values = []
        
        for i in range(fenetre, len(sequence_index5), fenetre//4):
            sous_sequence = sequence_index5[i-fenetre:i]
            
            # Extraire les composants
            sync_desync = [s.split('_')[0] for s in sous_sequence]
            cartes = [s.split('_')[1] for s in sous_sequence]
            resultats = [s.split('_')[2] for s in sous_sequence]
            
            # Calculer les proportions observées
            total = len(sous_sequence)
            prop_sync = sync_desync.count('0') / total
            prop_desync = sync_desync.count('1') / total
            prop_a = cartes.count('A') / total
            prop_b = cartes.count('B') / total
            prop_c = cartes.count('C') / total
            prop_banker = resultats.count('BANKER') / total
            prop_player = resultats.count('PLAYER') / total
            prop_tie = resultats.count('TIE') / total
            
            # Calculer les écarts par rapport aux proportions de référence
            ecarts = [
                abs(prop_sync - self.proportions_reference['SYNC']),
                abs(prop_desync - self.proportions_reference['DESYNC']),
                abs(prop_a - self.proportions_reference['A']),
                abs(prop_b - self.proportions_reference['B']),
                abs(prop_c - self.proportions_reference['C']),
                abs(prop_banker - self.proportions_reference['BANKER']),
                abs(prop_player - self.proportions_reference['PLAYER']),
                abs(prop_tie - self.proportions_reference['TIE'])
            ]
            
            # ICF = 1 - moyenne des écarts (plus c'est proche de 1, plus c'est cohérent)
            icf = 1 - np.mean(ecarts)
            icf_values.append(icf)
        
        return icf_values
    
    def detecter_ruptures_coherence(self, icf_values, seuil=0.02):
        """
        Détecte les ruptures dans la cohérence fractale
        """
        ruptures = []
        moyenne_icf = np.mean(icf_values)
        
        for i, icf in enumerate(icf_values):
            if abs(icf - moyenne_icf) > seuil:
                ruptures.append({
                    'position': i,
                    'icf': icf,
                    'deviation': abs(icf - moyenne_icf),
                    'type': 'rupture_haute' if icf > moyenne_icf else 'rupture_basse'
                })
        
        return ruptures

2. Coefficient de Persistance Temporelle (CPT)

class CoefficientPersistanceTemporelle:
    def __init__(self):
        self.transitions_reference = {
            'C_to_flip': 1.0,  # C change toujours l'index1
            'A_to_same': 1.0,  # A garde toujours l'index1
            'B_to_same': 1.0   # B garde toujours l'index1
        }
    
    def calculer_hurst_exponent(self, serie):
        """
        Calcule l'exposant de Hurst pour mesurer la persistance
        """
        if len(serie) < 50:
            return 0.5
        
        n = len(serie)
        rs_values = []
        
        for k in range(10, n//4):
            # Diviser la série en segments de taille k
            segments = [serie[i:i+k] for i in range(0, n-k+1, k)]
            
            rs_segment = []
            for segment in segments:
                if len(segment) == k:
                    mean_seg = np.mean(segment)
                    deviations = np.cumsum(segment - mean_seg)
                    
                    if len(deviations) > 0:
                        R = max(deviations) - min(deviations)
                        S = np.std(segment)
                        if S > 0:
                            rs_segment.append(R/S)
            
            if rs_segment:
                rs_values.append((k, np.mean(rs_segment)))
        
        if len(rs_values) < 2:
            return 0.5
        
        # Régression linéaire log-log
        log_k = [math.log(k) for k, _ in rs_values]
        log_rs = [math.log(rs) for _, rs in rs_values if rs > 0]
        
        if len(log_k) != len(log_rs) or len(log_k) < 2:
            return 0.5
        
        slope, _, _, _, _ = stats.linregress(log_k[:len(log_rs)], log_rs)
        return slope
    
    def calculer_cpt(self, sequence_index5, fenetre=500):
        """
        Calcule le Coefficient de Persistance Temporelle
        """
        cpt_values = []
        
        for i in range(fenetre, len(sequence_index5), fenetre//4):
            sous_sequence = sequence_index5[i-fenetre:i]
            
            # Analyser les transitions SYNC/DESYNC
            transitions = []
            for j in range(1, len(sous_sequence)):
                prev_sync = sous_sequence[j-1].split('_')[0]
                curr_sync = sous_sequence[j].split('_')[0]
                prev_carte = sous_sequence[j-1].split('_')[1]
                
                # Selon les règles mécaniques
                if prev_carte == 'C':
                    expected_flip = '0' if prev_sync == '1' else '1'
                    transitions.append(1 if curr_sync == expected_flip else -1)
                else:  # A ou B
                    transitions.append(1 if curr_sync == prev_sync else -1)
            
            # Calculer l'exposant de Hurst sur les transitions
            if transitions:
                hurst = self.calculer_hurst_exponent(transitions)
                
                # Analyser la persistance des patterns
                persistance_patterns = self.analyser_persistance_patterns(sous_sequence)
                
                # CPT combine Hurst et persistance des patterns
                cpt = (hurst + persistance_patterns) / 2
                cpt_values.append(cpt)
        
        return cpt_values
    
    def analyser_persistance_patterns(self, sequence):
        """
        Analyse la persistance des micro-patterns
        """
        patterns_2 = defaultdict(list)
        patterns_3 = defaultdict(list)
        
        # Patterns de longueur 2
        for i in range(len(sequence) - 1):
            pattern = (sequence[i], sequence[i+1])
            patterns_2[pattern].append(i)
        
        # Patterns de longueur 3
        for i in range(len(sequence) - 2):
            pattern = (sequence[i], sequence[i+1], sequence[i+2])
            patterns_3[pattern].append(i)
        
        # Calculer la persistance
        persistance_2 = np.mean([len(positions) for positions in patterns_2.values()])
        persistance_3 = np.mean([len(positions) for positions in patterns_3.values()])
        
        # Normaliser
        persistance_norm = (persistance_2 + persistance_3) / (len(sequence) / 2)
        
        return min(persistance_norm, 1.0)

3. Entropie Hiérarchique Adaptative (EHA)

class EntropieHierarchique:
    def __init__(self):
        self.entropies_reference = {
            'niveau_1': self.calculer_entropie([0.4968, 0.5032]),  # SYNC/DESYNC
            'niveau_2': self.calculer_entropie([0.3790, 0.3177, 0.3033]),  # A,B,C
            'niveau_3': self.calculer_entropie([0.4586, 0.4463, 0.0951])   # BANKER,PLAYER,TIE
        }
    
    def calculer_entropie(self, probabilities):
        """
        Calcule l'entropie de Shannon
        """
        return -sum(p * math.log2(p) for p in probabilities if p > 0)
    
    def calculer_eha(self, sequence_index5, fenetre=1000):
        """
        Calcule l'Entropie Hiérarchique Adaptative
        """
        eha_values = []
        
        for i in range(fenetre, len(sequence_index5), fenetre//4):
            sous_sequence = sequence_index5[i-fenetre:i]
            
            # Niveau 1: SYNC/DESYNC
            sync_counts = defaultdict(int)
            for s in sous_sequence:
                sync_counts[s.split('_')[0]] += 1
            
            total = len(sous_sequence)
            prob_sync = [sync_counts[k] / total for k in ['0', '1']]
            entropie_1 = self.calculer_entropie(prob_sync)
            
            # Niveau 2: A,B,C
            carte_counts = defaultdict(int)
            for s in sous_sequence:
                carte_counts[s.split('_')[1]] += 1
            
            prob_cartes = [carte_counts[k] / total for k in ['A', 'B', 'C']]
            entropie_2 = self.calculer_entropie(prob_cartes)
            
            # Niveau 3: BANKER,PLAYER,TIE
            result_counts = defaultdict(int)
            for s in sous_sequence:
                result_counts[s.split('_')[2]] += 1
            
            prob_results = [result_counts[k] / total for k in ['BANKER', 'PLAYER', 'TIE']]
            entropie_3 = self.calculer_entropie(prob_results)
            
            # Entropie conditionnelle (niveau 4: combinaisons complètes)
            pattern_counts = defaultdict(int)
            for s in sous_sequence:
                pattern_counts[s] += 1
            
            prob_patterns = [count / total for count in pattern_counts.values()]
            entropie_4 = self.calculer_entropie(prob_patterns)
            
            # Calculer les déviations par rapport aux références
            dev_1 = abs(entropie_1 - self.entropies_reference['niveau_1'])
            dev_2 = abs(entropie_2 - self.entropies_reference['niveau_2'])
            dev_3 = abs(entropie_3 - self.entropies_reference['niveau_3'])
            
            # EHA = entropie pondérée - déviations
            eha = (entropie_1 + entropie_2 + entropie_3 + entropie_4) / 4 - (dev_1 + dev_2 + dev_3) / 3
            eha_values.append(eha)
        
        return eha_values
    
    def detecter_anomalies_entropie(self, eha_values, seuil_z=2.0):
        """
        Détecte les anomalies dans l'entropie hiérarchique
        """
        moyenne = np.mean(eha_values)
        std_dev = np.std(eha_values)
        
        anomalies = []
        for i, eha in enumerate(eha_values):
            z_score = abs(eha - moyenne) / std_dev if std_dev > 0 else 0
            if z_score > seuil_z:
                anomalies.append({
                    'position': i,
                    'eha': eha,
                    'z_score': z_score,
                    'type': 'entropie_haute' if eha > moyenne else 'entropie_basse'
                })
        
        return anomalies

4. Indice de Chaos Contrôlé (ICC)

class IndicesChaosControle:
    def __init__(self):
        self.lyapunov_reference = 0.1  # Valeur de référence pour le chaos contrôlé
        self.stabilite_reference = 0.95
    
    def calculer_lyapunov_approx(self, serie):
        """
        Approximation de l'exposant de Lyapunov
        """
        if len(serie) < 100:
            return 0
        
        # Convertir la série en valeurs numériques
        numeric_series = []
        for s in serie:
            # Hachage simple pour convertir string en nombre
            hash_val = sum(ord(c) for c in s) % 1000
            numeric_series.append(hash_val)
        
        # Calculer les divergences
        divergences = []
        for i in range(1, len(numeric_series)):
            if numeric_series[i-1] != 0:
                divergence = abs(numeric_series[i] - numeric_series[i-1]) / abs(numeric_series[i-1])
                if divergence > 0:
                    divergences.append(math.log(divergence))
        
        return np.mean(divergences) if divergences else 0
    
    def calculer_stabilite_systemique(self, sequence_index5, fenetre_courte=100):
        """
        Mesure la stabilité du système via les transitions
        """
        conformites = []
        
        for i in range(1, len(sequence_index5)):
            prev_index1 = sequence_index5[i-1].split('_')[0]
            prev_index2 = sequence_index5[i-1].split('_')[1]
            curr_index1 = sequence_index5[i].split('_')[0]
            
            # Vérifier la conformité aux règles mécaniques
            if prev_index2 == 'C':
                expected = '0' if prev_index1 == '1' else '1'
                conformites.append(1 if curr_index1 == expected else 0)
            else:  # A ou B
                conformites.append(1 if curr_index1 == prev_index1 else 0)
        
        # Calculer la stabilité sur fenêtres glissantes
        stabilites = []
        for i in range(fenetre_courte, len(conformites), fenetre_courte//4):
            fenetre = conformites[i-fenetre_courte:i]
            stabilite = sum(fenetre) / len(fenetre)
            stabilites.append(stabilite)
        
        return stabilites
    
    def calculer_icc(self, sequence_index5, fenetre=1000):
        """
        Calcule l'Indice de Chaos Contrôlé
        """
        icc_values = []
        
        for i in range(fenetre, len(sequence_index5), fenetre//4):
            sous_sequence = sequence_index5[i-fenetre:i]
            
            # Composante 1: Exposant de Lyapunov approché
            lyapunov = self.calculer_lyapunov_approx(sous_sequence)
            
            # Composante 2: Stabilité systémique
            stabilites = self.calculer_stabilite_systemique(sous_sequence)
            stabilite_moyenne = np.mean(stabilites) if stabilites else 0
            
            # Composante 3: Variabilité contrôlée
            variabilite = self.calculer_variabilite_controlee(sous_sequence)
            
            # Composante 4: Entropie locale
            entropie_locale = self.calculer_entropie_locale(sous_sequence)
            
            # ICC combine tous les facteurs
            # Plus l'ICC est élevé, plus le système est dans un état de "chaos contrôlé"
            icc = (
                abs(lyapunov - self.lyapunov_reference) * 0.3 +
                stabilite_moyenne * 0.4 +
                variabilite * 0.2 +
                entropie_locale * 0.1
            )
            
            icc_values.append(icc)
        
        return icc_values
    
    def calculer_variabilite_controlee(self, sequence):
        """
        Mesure la variabilité tout en respectant les contraintes
        """
        # Analyser la distribution des 18 patterns
        pattern_counts = defaultdict(int)
        for s in sequence:
            pattern_counts[s] += 1
        
        # Calculer le coefficient de variation
        counts = list(pattern_counts.values())
        if len(counts) > 1:
            cv = np.std(counts) / np.mean(counts)
            return min(cv, 1.0)  # Normaliser
        return 0
    
    def calculer_entropie_locale(self, sequence):
        """
        Calcule l'entropie sur des sous-fenêtres
        """
        entropies = []
        fenetre_locale = 50
        
        for i in range(fenetre_locale, len(sequence), fenetre_locale//2):
            sous_seq = sequence[i-fenetre_locale:i]
            
            pattern_counts = defaultdict(int)
            for s in sous_seq:
                pattern_counts[s] += 1
            
            total = len(sous_seq)
            probabilities = [count / total for count in pattern_counts.values()]
            
            entropie = -sum(p * math.log2(p) for p in probabilities if p > 0)
            entropies.append(entropie)
        
        return np.mean(entropies) if entropies else 0
    
    def detecter_transitions_chaos(self, icc_values, seuil=0.1):
        """
        Détecte les transitions vers des états chaotiques
        """
        transitions = []
        
        for i in range(1, len(icc_values)):
            variation = abs(icc_values[i] - icc_values[i-1])
            if variation > seuil:
                transitions.append({
                    'position': i,
                    'icc_avant': icc_values[i-1],
                    'icc_apres': icc_values[i],
                    'variation': variation,
                    'type': 'vers_chaos' if icc_values[i] > icc_values[i-1] else 'vers_ordre'
                })
        
        return transitions

Classe principale pour utiliser toutes les métriques

class AnalyseurMetriquesFractales:
    def __init__(self):
        self.icf = IndiceCoherenceFractale()
        self.cpt = CoefficientPersistanceTemporelle()
        self.eha = EntropieHierarchique()
        self.icc = IndicesChaosControle()
    
    def analyser_sequence_complete(self, sequence_index5, fenetre=1000):
        """
        Analyse complète avec toutes les métriques
        """
        print("🔍 Analyse des métriques fractales en cours...")
        
        # Calculer toutes les métriques
        icf_values = self.icf.calculer_icf(sequence_index5, fenetre)
        cpt_values = self.cpt.calculer_cpt(sequence_index5, fenetre)
        eha_values = self.eha.calculer_eha(sequence_index5, fenetre)
        icc_values = self.icc.calculer_icc(sequence_index5, fenetre)
        
        # Détections d'anomalies
        ruptures_icf = self.icf.detecter_ruptures_coherence(icf_values)
        anomalies_eha = self.eha.detecter_anomalies_entropie(eha_values)
        transitions_icc = self.icc.detecter_transitions_chaos(icc_values)
        
        return {
            'metriques': {
                'ICF': icf_values,
                'CPT': cpt_values,
                'EHA': eha_values,
                'ICC': icc_values
            },
            'anomalies': {
                'ruptures_coherence': ruptures_icf,
                'anomalies_entropie': anomalies_eha,
                'transitions_chaos': transitions_icc
            },
            'statistiques': {
                'ICF_moyenne': np.mean(icf_values),
                'CPT_moyenne': np.mean(cpt_values),
                'EHA_moyenne': np.mean(eha_values),
                'ICC_moyenne': np.mean(icc_values)
            }
        }

# Exemple d'utilisation
if __name__ == "__main__":
    # Exemple de séquence (remplacez par vos vraies données)
    sequence_exemple = [
        "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_A_BANKER", 
        "1_B_PLAYER", "1_C_BANKER", "0_A_PLAYER", "0_B_TIE"
    ] * 1000  # Répéter pour avoir assez de données
    
    analyseur = AnalyseurMetriquesFractales()
    resultats = analyseur.analyser_sequence_complete(sequence_exemple)
    
    print("\n📊 Résultats des métriques fractales:")
    for metrique, valeur in resultats['statistiques'].items():
        print(f"{metrique}: {valeur:.4f}")