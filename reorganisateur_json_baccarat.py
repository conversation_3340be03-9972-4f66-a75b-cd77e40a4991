#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RÉORGANISATEUR JSON BACCARAT LUPASCO
====================================

Ce programme charge un fichier JSON généré par generateur_parties_baccarat.py
et le réorganise dans un format condensé plus facilement exploitable.

Le nouveau format contient pour chaque partie :
- Le numéro de la partie
- Les statistiques de la partie  
- Les informations de brûlage (burn_cards_count, burn_parity, initial_sync_state, index1)
- Une ligne pour la main null (alignement des indices)
- Pour chaque main : main_number, manche_pb_number, index1, index2, index3, index5

Auteur: Assistant IA
Date: 2025-07-03
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any

class ReorganisateurJSONBaccarat:
    """
    Réorganise les fichiers JSON de parties de baccarat Lupasco
    en format condensé plus facilement exploitable
    """
    
    def __init__(self):
        """Initialise le réorganisateur"""
        self.data_input = None
        self.data_output = None
        
    def charger_fichier_json(self, chemin_fichier: str) -> bool:
        """
        Charge le fichier JSON d'entrée
        
        Args:
            chemin_fichier: Chemin vers le fichier JSON à charger
            
        Returns:
            bool: True si le chargement a réussi
        """
        try:
            print(f"📂 Chargement du fichier : {chemin_fichier}")
            
            if not os.path.exists(chemin_fichier):
                print(f"❌ Erreur : Le fichier {chemin_fichier} n'existe pas")
                return False
                
            with open(chemin_fichier, 'r', encoding='utf-8') as f:
                self.data_input = json.load(f)
                
            nb_parties = len(self.data_input.get('parties', []))
            print(f"✅ Fichier chargé avec succès : {nb_parties} parties trouvées")
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur de format JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False
    
    def reorganiser_parties(self) -> bool:
        """
        Réorganise les parties en format condensé
        
        Returns:
            bool: True si la réorganisation a réussi
        """
        if not self.data_input:
            print("❌ Aucune donnée chargée")
            return False
            
        print("🔄 Début de la réorganisation...")
        
        # Créer la structure de sortie
        self.data_output = {
            "metadata": {
                "generateur": "RÉORGANISATEUR JSON BACCARAT LUPASCO",
                "version": "1.0",
                "date_reorganisation": datetime.now().isoformat(),
                "source_original": self.data_input.get('metadata', {}),
                "description": "Données réorganisées en format condensé pour exploitation facile"
            },
            "parties_condensees": []
        }
        
        # Traiter chaque partie
        for partie_orig in self.data_input.get('parties', []):
            partie_condensee = self._reorganiser_partie(partie_orig)
            self.data_output["parties_condensees"].append(partie_condensee)
            
        nb_parties = len(self.data_output["parties_condensees"])
        print(f"✅ Réorganisation terminée : {nb_parties} parties traitées")
        return True
    
    def _reorganiser_partie(self, partie_orig: Dict) -> Dict:
        """
        Réorganise une partie individuelle
        
        Args:
            partie_orig: Données originales de la partie
            
        Returns:
            Dict: Partie réorganisée en format condensé
        """
        partie_number = partie_orig.get('partie_number')
        burn_info = partie_orig.get('burn_info', {})
        statistiques = partie_orig.get('statistiques', {})
        mains_orig = partie_orig.get('mains', [])
        
        # Créer la structure condensée de la partie
        partie_condensee = {
            "partie_number": partie_number,
            "statistiques": {
                "total_mains": statistiques.get('total_mains', 0),
                "total_manches_pb": statistiques.get('total_manches_pb', 0),
                "total_ties": statistiques.get('total_ties', 0),
                "cut_card_atteinte": statistiques.get('cut_card_atteinte', False),
                "cartes_restantes": statistiques.get('cartes_restantes', 0)
            },
            "burn_cards_count": burn_info.get('burn_cards_count', 0),
            "burn_parity": burn_info.get('burn_parity', ''),
            "initial_sync_state": burn_info.get('initial_sync_state', 0),
            "index1_brulage": burn_info.get('initial_sync_state', 0),
            "mains_condensees": []
        }
        
        # Traiter chaque main
        for main_orig in mains_orig:
            main_condensee = self._reorganiser_main(main_orig)
            partie_condensee["mains_condensees"].append(main_condensee)
            
        return partie_condensee
    
    def _reorganiser_main(self, main_orig: Dict) -> Dict:
        """
        Réorganise une main individuelle en format condensé
        
        Args:
            main_orig: Données originales de la main
            
        Returns:
            Dict: Main réorganisée en format condensé
        """
        return {
            "main_number": main_orig.get('main_number'),
            "manche_pb_number": main_orig.get('manche_pb_number'),
            "index1": main_orig.get('index1'),
            "index2": main_orig.get('index2', ''),
            "index3": main_orig.get('index3', ''),
            "index5": main_orig.get('index5', '')
        }
    
    def sauvegarder_fichier_json(self, chemin_sortie: str) -> bool:
        """
        Sauvegarde le fichier JSON réorganisé
        
        Args:
            chemin_sortie: Chemin de sauvegarde du fichier réorganisé
            
        Returns:
            bool: True si la sauvegarde a réussi
        """
        if not self.data_output:
            print("❌ Aucune donnée réorganisée à sauvegarder")
            return False
            
        try:
            print(f"💾 Sauvegarde vers : {chemin_sortie}")
            
            with open(chemin_sortie, 'w', encoding='utf-8') as f:
                json.dump(self.data_output, f, indent=2, ensure_ascii=False)
                
            print(f"✅ Fichier sauvegardé avec succès")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde : {e}")
            return False
    
    def generer_format_tableau(self, chemin_sortie_txt: str = None) -> bool:
        """
        Génère un format tableau lisible pour vérification
        
        Args:
            chemin_sortie_txt: Chemin optionnel pour sauvegarder le tableau
            
        Returns:
            bool: True si la génération a réussi
        """
        if not self.data_output:
            print("❌ Aucune donnée réorganisée disponible")
            return False
            
        print("📋 Génération du format tableau...")
        
        tableau_lines = []
        tableau_lines.append("RÉORGANISATEUR JSON BACCARAT LUPASCO - FORMAT TABLEAU")
        tableau_lines.append("=" * 80)
        tableau_lines.append(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        tableau_lines.append("")
        
        for partie in self.data_output["parties_condensees"]:
            tableau_lines.append(f"PARTIE {partie['partie_number']}")
            tableau_lines.append(f"Statistiques: {partie['statistiques']['total_mains']} mains, "
                                f"{partie['statistiques']['total_manches_pb']} P/B, "
                                f"{partie['statistiques']['total_ties']} TIE")
            tableau_lines.append(f"Brûlage: {partie['burn_cards_count']} cartes, "
                                f"parité {partie['burn_parity']}, "
                                f"état initial {partie['initial_sync_state']}, "
                                f"index1: {partie['index1_brulage']}")
            tableau_lines.append("")
            
            # En-tête du tableau
            tableau_lines.append("Main | Manche | INDEX1 | INDEX2 | INDEX3  | INDEX5")
            tableau_lines.append("-" * 60)
            
            for main in partie["mains_condensees"]:
                main_num = str(main["main_number"]) if main["main_number"] is not None else "null"
                manche_num = str(main["manche_pb_number"]) if main["manche_pb_number"] is not None else "null"
                index1 = str(main["index1"]) if main["index1"] is not None else "null"
                index2 = main["index2"] if main["index2"] else "null" if main["main_number"] is None else main["index2"]
                index3 = main["index3"] if main["index3"] else "null" if main["main_number"] is None else main["index3"]
                index5 = main["index5"] if main["index5"] else "null" if main["main_number"] is None else main["index5"]

                ligne = f"{main_num:4s} | {manche_num:6s} | {index1:6s} | {index2:6s} | {index3:7s} | {index5}"
                tableau_lines.append(ligne)
            
            tableau_lines.append("")
            tableau_lines.append("-" * 80)
            tableau_lines.append("")
        
        # Afficher le tableau
        for ligne in tableau_lines:
            print(ligne)
        
        # Sauvegarder si demandé
        if chemin_sortie_txt:
            try:
                with open(chemin_sortie_txt, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(tableau_lines))
                print(f"✅ Tableau sauvegardé vers : {chemin_sortie_txt}")
            except Exception as e:
                print(f"❌ Erreur lors de la sauvegarde du tableau : {e}")
                return False
        
        return True

def reorganiser_fichier_json(fichier_entree: str, fichier_sortie: str = None,
                           generer_tableau: bool = True) -> bool:
    """
    Fonction principale pour réorganiser un fichier JSON

    Args:
        fichier_entree: Chemin vers le fichier JSON d'entrée
        fichier_sortie: Chemin de sortie (optionnel, auto-généré si None)
        generer_tableau: Si True, génère aussi un fichier tableau lisible

    Returns:
        bool: True si la réorganisation a réussi
    """
    print("🚀 RÉORGANISATEUR JSON BACCARAT LUPASCO")
    print("=" * 50)

    # Créer le réorganisateur
    reorganisateur = ReorganisateurJSONBaccarat()

    # Charger le fichier d'entrée
    if not reorganisateur.charger_fichier_json(fichier_entree):
        return False

    # Réorganiser les données
    if not reorganisateur.reorganiser_parties():
        return False

    # Générer le nom de fichier de sortie si non fourni
    if fichier_sortie is None:
        base_name = os.path.splitext(fichier_entree)[0]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        fichier_sortie = f"{base_name}_reorganise_{timestamp}.json"

    # Sauvegarder le fichier JSON réorganisé
    if not reorganisateur.sauvegarder_fichier_json(fichier_sortie):
        return False

    # Générer le tableau lisible si demandé
    if generer_tableau:
        fichier_tableau = os.path.splitext(fichier_sortie)[0] + "_tableau.txt"
        reorganisateur.generer_format_tableau(fichier_tableau)

    print("✅ Réorganisation terminée avec succès !")
    print(f"📄 Fichier JSON réorganisé : {fichier_sortie}")
    if generer_tableau:
        print(f"📋 Fichier tableau : {fichier_tableau}")

    return True

def main():
    """Fonction principale avec interface utilisateur"""
    print("🎯 RÉORGANISATEUR JSON BACCARAT LUPASCO")
    print("=" * 60)
    print("Ce programme réorganise les fichiers JSON générés par")
    print("generateur_parties_baccarat.py en format condensé exploitable.")
    print()

    # Demander le fichier d'entrée
    while True:
        fichier_entree = input("📂 Entrez le chemin du fichier JSON d'entrée : ").strip()

        if not fichier_entree:
            print("❌ Veuillez entrer un chemin de fichier")
            continue

        if not os.path.exists(fichier_entree):
            print(f"❌ Le fichier {fichier_entree} n'existe pas")
            continue

        if not fichier_entree.lower().endswith('.json'):
            print("❌ Le fichier doit avoir l'extension .json")
            continue

        break

    # Demander le fichier de sortie (optionnel)
    print()
    fichier_sortie = input("💾 Entrez le chemin de sortie (Entrée pour auto-générer) : ").strip()
    if not fichier_sortie:
        fichier_sortie = None

    # Demander si on génère le tableau
    print()
    reponse_tableau = input("📋 Générer un fichier tableau lisible ? (O/n) : ").strip().lower()
    generer_tableau = reponse_tableau != 'n'

    print()
    print("🔄 Début de la réorganisation...")

    # Lancer la réorganisation
    succes = reorganiser_fichier_json(fichier_entree, fichier_sortie, generer_tableau)

    if succes:
        print()
        print("🎉 Réorganisation terminée avec succès !")
    else:
        print()
        print("❌ Échec de la réorganisation")
        return 1

    return 0

# Exemple d'utilisation directe
def exemple_utilisation():
    """Exemple d'utilisation du réorganisateur"""
    print("📝 EXEMPLE D'UTILISATION")
    print("=" * 30)

    # Fichiers d'exemple
    fichier_test = "dataset_baccarat_lupasco_20250703_151439.json"

    if os.path.exists(fichier_test):
        print(f"🧪 Test avec le fichier : {fichier_test}")
        reorganiser_fichier_json(fichier_test)
    else:
        print(f"❌ Fichier de test non trouvé : {fichier_test}")
        print("💡 Utilisez la fonction main() pour une utilisation interactive")

if __name__ == "__main__":
    # Lancer l'interface utilisateur
    import sys

    if len(sys.argv) > 1:
        # Mode ligne de commande
        fichier_entree = sys.argv[1]
        fichier_sortie = sys.argv[2] if len(sys.argv) > 2 else None

        print(f"🚀 Mode ligne de commande")
        print(f"📂 Fichier d'entrée : {fichier_entree}")
        if fichier_sortie:
            print(f"💾 Fichier de sortie : {fichier_sortie}")

        succes = reorganiser_fichier_json(fichier_entree, fichier_sortie)
        sys.exit(0 if succes else 1)
    else:
        # Mode interactif
        sys.exit(main())
