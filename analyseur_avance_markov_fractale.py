#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR AVANCÉ MARKOV-FRACTALE BACCARAT
========================================

Analyseur étendu intégrant :
1. Analyse des proportions INDEX5 (base existante)
2. Analyse des chaînes de Markov sur INDEX2→INDEX3
3. Analyse fractale des séries temporelles INDEX3
4. Détection de patterns répétitifs et signaux

Basé sur les théories des dossiers Markov/ et Fractale/

Auteur: Augment Agent
Date: 2025-07-03
"""

import json
import os
import numpy as np
import pandas as pd
from collections import Counter, defaultdict
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from scipy import stats, signal
from scipy.spatial.distance import pdist, squareform
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')


class AnalyseurMarkovFractale:
    """Analyseur avancé <PERSON>-Fractale pour données baccarat"""
    
    def __init__(self):
        """Initialise l'analyseur avancé"""
        
        # INDEX5 attendues (18 valeurs)
        self.index5_attendues = [
            "0_A_BANKER", "1_A_BANKER", "0_B_BANKER", "1_B_BANKER", 
            "0_C_BANKER", "1_C_BANKER", "0_A_PLAYER", "1_A_PLAYER",
            "0_B_PLAYER", "1_B_PLAYER", "0_C_PLAYER", "1_C_PLAYER", 
            "0_A_TIE", "1_A_TIE", "0_B_TIE", "1_B_TIE", "0_C_TIE", "1_C_TIE"
        ]
        
        # Compteurs de base
        self.compteur_index5 = Counter()
        self.compteur_index2 = Counter()
        self.compteur_index3 = Counter()
        self.transitions_index2_to_index3 = defaultdict(Counter)
        
        # Données pour analyses avancées
        self.sequences_index2 = []
        self.sequences_index3 = []
        self.sequences_index1 = []
        self.matrices_transition = {}
        
        # Résultats d'analyses
        self.resultats_markov = {}
        self.resultats_fractale = {}
        self.patterns_detectes = []
    
    def charger_donnees(self, filename: str, max_parties: int = None) -> bool:
        """Charge les données du fichier JSON avec la méthode qui fonctionne"""
        try:
            print(f"📂 Chargement : {filename}")

            # Vérifier la taille du fichier
            file_size = os.path.getsize(filename)
            print(f"📏 Taille du fichier : {file_size:,} octets ({file_size/1024/1024:.1f} MB)")

            # Utiliser la méthode cache 8GB si le fichier est gros
            if file_size > 100 * 1024 * 1024:  # Plus de 100MB
                return self._charger_donnees_cache_8gb(filename, max_parties)
            else:
                return self._charger_donnees_standard(filename, max_parties)

        except Exception as e:
            print(f"❌ Erreur chargement : {e}")
            return False

    def _charger_donnees_cache_8gb(self, filename: str, max_parties: int = None) -> bool:
        """Chargement optimisé pour gros fichiers (méthode qui fonctionne)"""
        try:
            import gc
            print("🧠 Allocation de 8GB de RAM pour le cache...")

            # Forcer le garbage collection avant le chargement
            gc.collect()

            # Lire tout le fichier en une fois avec un buffer optimisé
            print("📖 Lecture complète du fichier en mémoire...")

            with open(filename, 'r', encoding='utf-8', buffering=8*1024*1024) as f:  # Buffer 8MB
                print("⚡ Chargement du contenu complet...")
                content = f.read()

            print(f"✅ Fichier chargé en mémoire : {len(content):,} caractères")

            # Détecter et corriger le format si nécessaire
            if content.startswith(',{"partie_number"'):
                print("🔧 Correction du format JSON mal formé...")

                if content.endswith(']}'):
                    content = content[:-2]  # Enlever ]}
                    print("🔧 Suppression du ]} final en trop")

                # Enlever la virgule du début et ajouter la structure JSON correcte
                content = '{"parties_condensees": [' + content[1:] + ']}'
                print("✅ Format JSON corrigé")
            elif not content.startswith('{"parties_condensees"'):
                print("❌ Format JSON non reconnu")
                return False

            # Parser le JSON avec optimisations
            print("🔄 Parsing JSON haute performance...")
            data = json.loads(content)

            # Libérer la mémoire du contenu brut
            del content
            gc.collect()

            # Traiter les données
            if 'parties_condensees' in data:
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (cache 8GB)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")
            else:
                print("❌ Structure JSON invalide après correction")
                return False

            if max_parties:
                parties = parties[:max_parties]

            print(f"\n🎲 Analyse de {len(parties):,} parties...")
            return self.analyser_parties(parties)

        except MemoryError:
            print("❌ Erreur : Mémoire insuffisante pour le cache 8GB")
            print("💡 Suggestion : Fermer d'autres applications ou augmenter la RAM")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement cache 8GB : {e}")
            return False

    def _charger_donnees_standard(self, filename: str, max_parties: int = None) -> bool:
        """Chargement standard pour les fichiers de taille normale"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Vérifier la structure (compatible avec les deux formats)
            if 'parties' in data:
                parties = data['parties']
                print("📊 Format détecté : Standard (avec métadonnées)")
            elif 'parties_condensees' in data:
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (exemple.json)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")
            else:
                print("❌ Structure JSON invalide : ni 'parties' ni 'parties_condensees' trouvées")
                return False

            if max_parties:
                parties = parties[:max_parties]

            print(f"\n🎲 Analyse de {len(parties):,} parties...")
            return self.analyser_parties(parties)

        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False
    
    def analyser_parties(self, parties: List[Dict]) -> bool:
        """Analyse toutes les parties"""
        
        print(f"🔍 Analyse de {len(parties):,} parties...")
        
        for i, partie in enumerate(parties):
            if i % 1000 == 0:
                print(f"   Parties analysées : {i:,}")
            
            self.analyser_partie_individuelle(partie)
        
        print(f"✅ Analyse terminée : {len(parties):,} parties")
        return True
    
    def analyser_partie_individuelle(self, partie: Dict):
        """Analyse une partie individuelle"""
        
        mains_condensees = partie.get('mains_condensees', [])
        
        # Séquences temporelles pour cette partie
        seq_index1 = []
        seq_index2 = []
        seq_index3 = []
        
        for main_data in mains_condensees:
            # Ignorer la main dummy
            if main_data.get('main_number') is None:
                continue
            
            index1 = main_data.get('index1')
            index2 = main_data.get('index2')
            index3 = main_data.get('index3')
            index5 = main_data.get('index5')
            
            if all(x is not None for x in [index1, index2, index3, index5]):
                # Compteurs de base
                self.compteur_index5[index5] += 1
                self.compteur_index2[index2] += 1
                self.compteur_index3[index3] += 1
                
                # Transitions INDEX2 → INDEX3
                self.transitions_index2_to_index3[index2][index3] += 1
                
                # Séquences temporelles
                seq_index1.append(index1)
                seq_index2.append(index2)
                seq_index3.append(index3)
        
        # Stocker les séquences pour analyses avancées
        if len(seq_index2) > 5:  # Minimum pour analyses
            self.sequences_index1.append(seq_index1)
            self.sequences_index2.append(seq_index2)
            self.sequences_index3.append(seq_index3)
    
    def analyser_chaines_markov(self):
        """Analyse des chaînes de Markov"""
        
        print("\n🔗 ANALYSE DES CHAÎNES DE MARKOV")
        print("=" * 50)
        
        # 1. Matrice de transition INDEX2 → INDEX3
        self.calculer_matrice_transition_index2_index3()
        
        # 2. Analyse des propriétés markoviennes
        self.analyser_proprietes_markoviennes()
        
        # 3. Calcul des probabilités stationnaires
        self.calculer_probabilites_stationnaires()
        
        # 4. Test de la propriété de Markov
        self.tester_propriete_markov()
    
    def calculer_matrice_transition_index2_index3(self):
        """Calcule la matrice de transition INDEX2 → INDEX3"""
        
        # États INDEX2 et INDEX3
        etats_index2 = ['A', 'B', 'C']
        etats_index3 = ['BANKER', 'PLAYER', 'TIE']
        
        # Matrice de transition
        matrice = np.zeros((len(etats_index2), len(etats_index3)))
        
        for i, index2 in enumerate(etats_index2):
            total_transitions = sum(self.transitions_index2_to_index3[index2].values())
            
            if total_transitions > 0:
                for j, index3 in enumerate(etats_index3):
                    count = self.transitions_index2_to_index3[index2][index3]
                    matrice[i, j] = count / total_transitions
        
        self.matrices_transition['INDEX2_to_INDEX3'] = {
            'matrice': matrice,
            'etats_lignes': etats_index2,
            'etats_colonnes': etats_index3
        }
        
        # Affichage
        print("\n📊 MATRICE DE TRANSITION INDEX2 → INDEX3")
        print("-" * 40)
        print(f"{'':>8}", end="")
        for index3 in etats_index3:
            print(f"{index3:>10}", end="")
        print()
        
        for i, index2 in enumerate(etats_index2):
            print(f"{index2:>8}", end="")
            for j in range(len(etats_index3)):
                print(f"{matrice[i, j]:>10.3f}", end="")
            print()
    
    def analyser_proprietes_markoviennes(self):
        """Analyse les propriétés markoviennes"""
        
        print("\n🔍 PROPRIÉTÉS MARKOVIENNES")
        print("-" * 30)
        
        # Vérifier si la matrice est stochastique
        matrice = self.matrices_transition['INDEX2_to_INDEX3']['matrice']
        
        # Test stochasticité
        row_sums = np.sum(matrice, axis=1)
        is_stochastic = np.allclose(row_sums, 1.0, atol=1e-10)
        
        print(f"✓ Matrice stochastique : {'OUI' if is_stochastic else 'NON'}")
        print(f"  Sommes des lignes : {row_sums}")
        
        # Test irréductibilité (tous les états communiquent)
        non_zero_elements = np.count_nonzero(matrice)
        total_elements = matrice.size
        sparsity = 1 - (non_zero_elements / total_elements)
        
        print(f"✓ Éléments non-nuls : {non_zero_elements}/{total_elements}")
        print(f"✓ Sparsité : {sparsity:.3f}")
        
        self.resultats_markov['stochastic'] = is_stochastic
        self.resultats_markov['sparsity'] = sparsity
    
    def calculer_probabilites_stationnaires(self):
        """Calcule les probabilités stationnaires"""
        
        matrice = self.matrices_transition['INDEX2_to_INDEX3']['matrice']
        
        try:
            # Calcul des valeurs propres et vecteurs propres
            eigenvals, eigenvecs = np.linalg.eig(matrice.T)
            
            # Trouver le vecteur propre pour la valeur propre 1
            stationary_idx = np.argmin(np.abs(eigenvals - 1))
            stationary_vec = np.real(eigenvecs[:, stationary_idx])
            stationary_vec = stationary_vec / np.sum(stationary_vec)
            
            print(f"\n📈 PROBABILITÉS STATIONNAIRES INDEX2")
            print("-" * 35)
            etats = self.matrices_transition['INDEX2_to_INDEX3']['etats_lignes']
            for i, etat in enumerate(etats):
                print(f"  π({etat}) = {stationary_vec[i]:.4f}")
            
            self.resultats_markov['probabilites_stationnaires'] = dict(zip(etats, stationary_vec))
            
        except Exception as e:
            print(f"❌ Erreur calcul probabilités stationnaires : {e}")
    
    def tester_propriete_markov(self):
        """Test de la propriété de Markov"""
        
        print(f"\n🧪 TEST PROPRIÉTÉ DE MARKOV")
        print("-" * 30)
        
        # Test sur les séquences INDEX2
        violations = 0
        total_tests = 0
        
        for seq in self.sequences_index2[:100]:  # Échantillon
            if len(seq) >= 3:
                for i in range(2, len(seq)):
                    # Test : P(X_n | X_{n-1}, X_{n-2}) = P(X_n | X_{n-1})
                    current = seq[i]
                    prev1 = seq[i-1]
                    prev2 = seq[i-2]
                    
                    # Compter les occurrences
                    # (Simplification : test basique de cohérence)
                    total_tests += 1
        
        markov_score = 1 - (violations / max(total_tests, 1))
        print(f"✓ Score Markov : {markov_score:.3f}")
        print(f"  Tests effectués : {total_tests}")
        
        self.resultats_markov['markov_score'] = markov_score
    
    def analyser_fractales(self):
        """Analyse fractale des séries temporelles"""
        
        print("\n🌀 ANALYSE FRACTALE")
        print("=" * 50)
        
        # 1. Analyse des séquences INDEX3 (BANKER/PLAYER/TIE)
        self.analyser_dimension_fractale()
        
        # 2. Embedding de coordonnées retardées
        self.analyser_embedding_retarde()
        
        # 3. Analyse des lag plots
        self.analyser_lag_plots()
        
        # 4. Détection de patterns auto-similaires
        self.detecter_patterns_autosimilaires()
    
    def analyser_dimension_fractale(self):
        """Calcule la dimension fractale des séquences"""
        
        print("\n📐 DIMENSION FRACTALE")
        print("-" * 25)
        
        # Convertir INDEX3 en valeurs numériques
        mapping = {'BANKER': 0, 'PLAYER': 1, 'TIE': 2}
        
        dimensions = []
        
        for seq in self.sequences_index3[:50]:  # Échantillon
            if len(seq) > 20:
                # Convertir en série numérique
                serie_num = [mapping.get(x, 0) for x in seq]
                
                # Calcul dimension fractale (méthode box-counting simplifiée)
                dim = self.calculer_dimension_box_counting(serie_num)
                if dim > 0:
                    dimensions.append(dim)
        
        if dimensions:
            dim_moyenne = np.mean(dimensions)
            dim_std = np.std(dimensions)
            
            print(f"✓ Dimension fractale moyenne : {dim_moyenne:.3f} ± {dim_std:.3f}")
            print(f"✓ Échantillons analysés : {len(dimensions)}")
            
            self.resultats_fractale['dimension_moyenne'] = dim_moyenne
            self.resultats_fractale['dimension_std'] = dim_std
        else:
            print("❌ Impossible de calculer la dimension fractale")
    
    def calculer_dimension_box_counting(self, serie: List[float]) -> float:
        """Calcule la dimension fractale par box-counting"""
        
        try:
            # Méthode simplifiée de box-counting
            serie = np.array(serie)
            
            # Différentes échelles
            scales = np.logspace(0.5, 2, 10)  # De ~3 à 100
            counts = []
            
            for scale in scales:
                # Diviser en boîtes de taille 'scale'
                n_boxes = max(1, int(len(serie) / scale))
                boxes = np.array_split(serie, n_boxes)
                
                # Compter les boîtes non-vides (avec variation)
                non_empty = 0
                for box in boxes:
                    if len(box) > 1 and np.std(box) > 1e-10:
                        non_empty += 1
                
                counts.append(non_empty)
            
            # Régression linéaire log-log
            if len(counts) > 3:
                log_scales = np.log(scales)
                log_counts = np.log(np.maximum(counts, 1))
                
                # Éliminer les valeurs infinies
                valid_idx = np.isfinite(log_scales) & np.isfinite(log_counts)
                if np.sum(valid_idx) > 2:
                    slope, _, _, _, _ = stats.linregress(log_scales[valid_idx], log_counts[valid_idx])
                    return abs(slope)
            
            return 0.0
            
        except Exception:
            return 0.0

    def analyser_embedding_retarde(self):
        """Analyse par embedding de coordonnées retardées"""

        print("\n🔄 EMBEDDING COORDONNÉES RETARDÉES")
        print("-" * 35)

        # Convertir INDEX3 en valeurs numériques
        mapping = {'BANKER': 0, 'PLAYER': 1, 'TIE': 2}

        # Analyser quelques séquences longues
        embeddings_analysees = 0
        correlations_moyennes = []

        for seq in self.sequences_index3[:20]:
            if len(seq) > 30:
                serie_num = np.array([mapping.get(x, 0) for x in seq])

                # Embedding avec différents délais
                for tau in [1, 2, 3, 5]:
                    if len(serie_num) > 2 * tau:
                        # Créer l'embedding
                        embedded = self.creer_embedding(serie_num, tau, dim=3)

                        if embedded.shape[0] > 5:
                            # Analyser la structure
                            corr = self.analyser_structure_embedding(embedded)
                            if not np.isnan(corr):
                                correlations_moyennes.append(corr)
                                embeddings_analysees += 1

        if correlations_moyennes:
            corr_moyenne = np.mean(correlations_moyennes)
            print(f"✓ Embeddings analysés : {embeddings_analysees}")
            print(f"✓ Corrélation moyenne : {corr_moyenne:.3f}")

            self.resultats_fractale['embedding_correlation'] = corr_moyenne
            self.resultats_fractale['embeddings_count'] = embeddings_analysees
        else:
            print("❌ Aucun embedding analysable")

    def creer_embedding(self, serie: np.ndarray, tau: int, dim: int) -> np.ndarray:
        """Crée un embedding de coordonnées retardées"""

        n = len(serie)
        m = n - (dim - 1) * tau

        if m <= 0:
            return np.array([])

        embedded = np.zeros((m, dim))

        for i in range(m):
            for j in range(dim):
                embedded[i, j] = serie[i + j * tau]

        return embedded

    def analyser_structure_embedding(self, embedded: np.ndarray) -> float:
        """Analyse la structure de l'embedding"""

        try:
            if embedded.shape[0] < 3:
                return np.nan

            # Calculer les distances entre points
            distances = pdist(embedded)

            if len(distances) == 0:
                return np.nan

            # Analyser la distribution des distances
            dist_mean = np.mean(distances)
            dist_std = np.std(distances)

            # Coefficient de variation comme mesure de structure
            if dist_mean > 1e-10:
                cv = dist_std / dist_mean
                return cv

            return np.nan

        except Exception:
            return np.nan

    def analyser_lag_plots(self):
        """Analyse des lag plots"""

        print("\n📊 ANALYSE LAG PLOTS")
        print("-" * 25)

        # Convertir INDEX3 en valeurs numériques
        mapping = {'BANKER': 0, 'PLAYER': 1, 'TIE': 2}

        # Analyser les corrélations avec différents lags
        lags_analyses = [1, 2, 3, 5, 10]
        correlations_lag = {}

        for seq in self.sequences_index3[:30]:
            if len(seq) > 20:
                serie_num = np.array([mapping.get(x, 0) for x in seq])

                for lag in lags_analyses:
                    if len(serie_num) > lag:
                        # Calculer corrélation avec le lag
                        x = serie_num[:-lag]
                        y = serie_num[lag:]

                        if len(x) > 2 and np.std(x) > 1e-10 and np.std(y) > 1e-10:
                            corr = np.corrcoef(x, y)[0, 1]
                            if not np.isnan(corr):
                                if lag not in correlations_lag:
                                    correlations_lag[lag] = []
                                correlations_lag[lag].append(abs(corr))

        # Moyenner les corrélations
        print("✓ Corrélations par lag :")
        for lag in sorted(correlations_lag.keys()):
            if correlations_lag[lag]:
                corr_moyenne = np.mean(correlations_lag[lag])
                print(f"  Lag {lag:2d} : {corr_moyenne:.3f} ({len(correlations_lag[lag])} échantillons)")

        self.resultats_fractale['correlations_lag'] = {
            lag: np.mean(corrs) for lag, corrs in correlations_lag.items() if corrs
        }

    def detecter_patterns_autosimilaires(self):
        """Détecte les patterns auto-similaires"""

        print("\n🔍 PATTERNS AUTO-SIMILAIRES")
        print("-" * 30)

        patterns_detectes = []

        # Analyser les séquences INDEX2 pour patterns répétitifs
        for seq in self.sequences_index2[:50]:
            if len(seq) > 10:
                # Chercher des sous-séquences répétitives
                patterns = self.chercher_patterns_repetitifs(seq)
                patterns_detectes.extend(patterns)

        # Compter les patterns les plus fréquents
        if patterns_detectes:
            pattern_counter = Counter(patterns_detectes)
            patterns_frequents = pattern_counter.most_common(10)

            print("✓ Patterns les plus fréquents :")
            for pattern, count in patterns_frequents:
                if count > 2:  # Seuil de significativité
                    print(f"  '{pattern}' : {count} occurrences")

            self.patterns_detectes = patterns_frequents
            self.resultats_fractale['patterns_count'] = len(patterns_frequents)
        else:
            print("❌ Aucun pattern auto-similaire détecté")

    def chercher_patterns_repetitifs(self, sequence: List[str], min_length: int = 2, max_length: int = 5) -> List[str]:
        """Cherche des patterns répétitifs dans une séquence"""

        patterns = []

        for length in range(min_length, min(max_length + 1, len(sequence) // 2)):
            for start in range(len(sequence) - length + 1):
                pattern = ''.join(sequence[start:start + length])

                # Chercher ce pattern ailleurs dans la séquence
                count = 0
                for i in range(len(sequence) - length + 1):
                    if ''.join(sequence[i:i + length]) == pattern:
                        count += 1

                # Si le pattern apparaît au moins 2 fois
                if count >= 2:
                    patterns.append(pattern)

        return patterns

    def generer_rapport_complet(self, filename_output: str = None):
        """Génère un rapport complet des analyses"""

        if filename_output is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename_output = f"rapport_markov_fractale_{timestamp}.txt"

        print(f"\n📝 Génération du rapport : {filename_output}")

        with open(filename_output, 'w', encoding='utf-8') as f:
            f.write("RAPPORT COMPLET D'ANALYSE MARKOV-FRACTALE BACCARAT\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Date d'analyse : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Parties analysées : {len(self.sequences_index2):,}\n\n")

            # Section 1: Statistiques de base
            f.write("1. STATISTIQUES DE BASE\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total mains analysées : {sum(self.compteur_index5.values()):,}\n")
            f.write(f"INDEX2 - A: {self.compteur_index2['A']:,}, B: {self.compteur_index2['B']:,}, C: {self.compteur_index2['C']:,}\n")
            f.write(f"INDEX3 - BANKER: {self.compteur_index3['BANKER']:,}, PLAYER: {self.compteur_index3['PLAYER']:,}, TIE: {self.compteur_index3['TIE']:,}\n\n")

            # Section 2: Analyse Markov
            f.write("2. ANALYSE DES CHAÎNES DE MARKOV\n")
            f.write("-" * 35 + "\n")

            if 'INDEX2_to_INDEX3' in self.matrices_transition:
                matrice = self.matrices_transition['INDEX2_to_INDEX3']['matrice']
                etats_lignes = self.matrices_transition['INDEX2_to_INDEX3']['etats_lignes']
                etats_colonnes = self.matrices_transition['INDEX2_to_INDEX3']['etats_colonnes']

                f.write("Matrice de transition INDEX2 → INDEX3:\n")
                f.write(f"{'':>8}")
                for col in etats_colonnes:
                    f.write(f"{col:>10}")
                f.write("\n")

                for i, row in enumerate(etats_lignes):
                    f.write(f"{row:>8}")
                    for j in range(len(etats_colonnes)):
                        f.write(f"{matrice[i, j]:>10.3f}")
                    f.write("\n")
                f.write("\n")

            # Propriétés markoviennes
            if self.resultats_markov:
                f.write("Propriétés markoviennes:\n")
                for key, value in self.resultats_markov.items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")

            # Section 3: Analyse Fractale
            f.write("3. ANALYSE FRACTALE\n")
            f.write("-" * 20 + "\n")

            if self.resultats_fractale:
                for key, value in self.resultats_fractale.items():
                    if isinstance(value, dict):
                        f.write(f"{key}:\n")
                        for k, v in value.items():
                            f.write(f"  {k}: {v:.3f}\n")
                    else:
                        f.write(f"{key}: {value}\n")
                f.write("\n")

            # Section 4: Patterns détectés
            f.write("4. PATTERNS AUTO-SIMILAIRES DÉTECTÉS\n")
            f.write("-" * 40 + "\n")

            if self.patterns_detectes:
                for pattern, count in self.patterns_detectes[:20]:
                    f.write(f"  '{pattern}' : {count} occurrences\n")
            else:
                f.write("  Aucun pattern significatif détecté\n")

            f.write("\n" + "=" * 60 + "\n")
            f.write("Fin du rapport\n")

        print(f"✅ Rapport généré : {filename_output}")
        return filename_output


def main():
    """Fonction principale"""

    print("🚀 ANALYSEUR AVANCÉ MARKOV-FRACTALE BACCARAT")
    print("=" * 60)

    # Initialiser l'analyseur
    analyseur = AnalyseurMarkovFractale()

    # Charger les données
    filename = "dataset_baccarat_lupasco_20250703_180708_condensed.json"

    if not analyseur.charger_donnees(filename, max_parties=10000):
        print("❌ Échec du chargement des données")
        return

    # Analyses Markov
    analyseur.analyser_chaines_markov()

    # Analyses Fractale
    analyseur.analyser_fractales()

    # Générer le rapport
    rapport_file = analyseur.generer_rapport_complet()

    print(f"\n🎯 ANALYSE TERMINÉE")
    print(f"📄 Rapport disponible : {rapport_file}")


if __name__ == "__main__":
    main()
