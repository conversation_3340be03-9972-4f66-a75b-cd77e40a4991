#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GÉNÉRATEUR DE PARTIES BACCARAT LUPASCO RÉALISTE
===============================================

Génère des parties de baccarat réalistes avec :
- Sabot de 416 cartes (8 decks de 52 cartes)
- Règles strictes du baccarat selon regles_baccarat_essentielles.txt
- Simulation complète des tirages de cartes
- Calcul automatique des INDEX Lupasco
- Export CSV pour analyse

Basé sur les règles officielles du baccarat, sans statistiques prédéfinies.
"""

import secrets  # CSPRNG cryptographiquement sécurisé
import os       # Entropie système
import hashlib  # Combinaison d'entropie
import time     # Timing haute précision
import json     # Pour export JSON
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

# Imports pour l'analyse scientifique révolutionnaire
import numpy as np
import scipy.stats as stats
import scipy.signal as signal
from scipy.fft import fft, fftfreq
from scipy.special import gamma, beta, erf, erfc
from scipy.optimize import minimize
from scipy.linalg import toeplitz
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mutual_info_score
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# ============================================================================
# CONFIGURATION DU SABOT BACCARAT
# ============================================================================

# Sabot de 8 decks standard
DECKS_COUNT = 8
CARDS_PER_DECK = 52
TOTAL_CARDS = DECKS_COUNT * CARDS_PER_DECK  # 416 cartes

# Cut card à 80% du sabot
CUT_CARD_POSITION = int(TOTAL_CARDS * 0.8)  # 332ème carte

# Valeurs des cartes pour le calcul des scores
CARD_VALUES = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 0, 'J': 0, 'Q': 0, 'K': 0
}

# Cartes pour le brûlage (valeur → nombre de cartes supplémentaires)
BURN_CARDS_COUNT = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 10, 'J': 10, 'Q': 10, 'K': 10
}

# Mapping nombre de cartes → catégories INDEX2
CARDS_MAPPING = {
    4: 'A',  # pair_4 = A
    5: 'C',  # impair_5 = C
    6: 'B'   # pair_6 = B
}

# ============================================================================
# STRUCTURES DE DONNÉES
# ============================================================================

@dataclass
class Carte:
    """Représente une carte du sabot"""
    rang: str      # 'A', '2', '3', ..., '10', 'J', 'Q', 'K'
    couleur: str   # '♠', '♥', '♦', '♣'
    valeur: int    # Valeur pour le calcul du score (0-9)

@dataclass
class MainBaccarat:
    """Structure d'une main de baccarat avec tous les INDEX Lupasco"""

    # Identification
    main_number: int
    manche_pb_number: Optional[int]  # None pour brûlage, même numéro pour TIE

    # Cartes distribuées
    cartes_player: List[Carte]
    cartes_banker: List[Carte]
    total_cartes_distribuees: int

    # Scores calculés
    score_player: int
    score_banker: int

    # INDEX Lupasco
    index1: int                      # 0 (SYNC) ou 1 (DESYNC)
    cards_count: int                 # 4, 5, ou 6 (ou nombre pour brûlage)
    index2: str                      # 'A', 'C', 'B' (ou '' pour brûlage)
    index3: str                      # 'PLAYER', 'BANKER', 'TIE' (ou '' pour brûlage)
    index5: str                      # INDEX1_INDEX2_INDEX3 (ex: '0_A_PLAYER')

    # Métadonnées
    timestamp: str = ""

@dataclass
class PartieBaccarat:
    """Structure d'une partie complète de baccarat"""

    # Identification
    partie_number: int

    # Sabot
    sabot_initial: List[Carte]
    cartes_restantes: int

    # Brûlage initial
    cartes_brulees: List[Carte]
    burn_cards_count: int
    burn_parity: str                 # 'PAIR' ou 'IMPAIR'
    initial_sync_state: int          # 0 (SYNC) ou 1 (DESYNC)

    # Mains de la partie
    mains: List[MainBaccarat]

    # Paramètres avec valeurs par défaut
    cut_card_atteinte: bool = False
    total_mains: int = 0
    total_manches_pb: int = 0
    total_ties: int = 0
    current_sync_state: int = 0  # SYNC
    premiere_carte_brulee: Optional[Carte] = None

# ============================================================================
# GÉNÉRATEUR DE HASARD CRYPTOGRAPHIQUEMENT SÉCURISÉ
# ============================================================================

class SecureRandomGenerator:
    """
    Générateur de hasard cryptographiquement sécurisé

    Combine plusieurs sources d'entropie pour un hasard optimal :
    - CSPRNG système (secrets)
    - Entropie hardware (os.urandom)
    - Timing haute précision
    - Adresses mémoire
    """

    def __init__(self, seed: Optional[int] = None):
        """Initialise le générateur sécurisé"""
        # Collecter entropie de multiples sources
        entropy_sources = [
            secrets.token_bytes(32),      # CSPRNG système
            os.urandom(32),               # Entropie hardware
            str(time.time_ns()).encode(), # Timing haute précision
            str(id(self)).encode(),       # Adresse mémoire
        ]

        # Si seed fourni, l'ajouter aux sources
        if seed is not None:
            entropy_sources.append(str(seed).encode())

        # Combiner toutes les sources avec SHA-256
        combined = b''.join(entropy_sources)
        self.entropy_pool = hashlib.sha256(combined).digest()

        print(f"Generateur securise initialise avec {len(entropy_sources)} sources d'entropie")

    def secure_uniform_int(self, max_val: int) -> int:
        """
        Génère un entier uniforme sans biais de modulo (rejection sampling)

        Utilise la méthode de rejection sampling pour éliminer complètement
        le biais de modulo et garantir une distribution parfaitement uniforme.

        Args:
            max_val: Valeur maximale (exclusive) - doit être > 0

        Returns:
            int: Entier uniforme dans [0, max_val-1]

        Raises:
            ValueError: Si max_val <= 0
        """
        if max_val <= 0:
            raise ValueError("max_val doit être strictement positif")

        # Cas trivial
        if max_val == 1:
            return 0

        # Calculer le nombre de bytes nécessaires
        range_size = max_val
        num_bytes = (range_size.bit_length() + 7) // 8

        # Rejection sampling pour éliminer le biais
        while True:
            random_bytes = secrets.token_bytes(num_bytes)
            random_int = int.from_bytes(random_bytes, 'big')

            # Calculer la limite valide pour éviter le biais
            max_valid = (2**(num_bytes * 8) // range_size) * range_size

            if random_int < max_valid:
                return random_int % range_size

    def secure_shuffle(self, deck: List) -> List:
        """
        Mélange cryptographiquement sécurisé (Fisher-Yates) SANS BIAIS

        Utilise la méthode de rejection sampling pour garantir une distribution
        parfaitement uniforme des cartes, éliminant tout biais statistique.

        Args:
            deck: Liste à mélanger

        Returns:
            List: Liste mélangée de façon cryptographiquement sécurisée et sans biais
        """
        deck_copy = deck.copy()

        for i in range(len(deck_copy) - 1, 0, -1):
            # Générer index aléatoire SANS BIAIS avec rejection sampling
            j = self.secure_uniform_int(i + 1)

            # Échanger les éléments
            deck_copy[i], deck_copy[j] = deck_copy[j], deck_copy[i]

        return deck_copy

    def refresh_entropy(self):
        """Rafraîchit le pool d'entropie"""
        new_entropy = [
            secrets.token_bytes(16),
            str(time.time_ns()).encode(),
            os.urandom(16)
        ]
        combined = self.entropy_pool + b''.join(new_entropy)
        self.entropy_pool = hashlib.sha256(combined).digest()

# ============================================================================
# GÉNÉRATEUR DE PARTIES RÉALISTE
# ============================================================================

class GenerateurPartiesBaccarat:
    """
    Générateur de parties de baccarat réalistes avec hasard cryptographiquement sécurisé

    Simule un vrai sabot de 416 cartes avec les règles strictes du baccarat.
    Utilise un générateur de hasard sécurisé pour un mélange optimal.
    Aucune statistique prédéfinie - tout est calculé selon les tirages réels.
    """

    def __init__(self, seed: Optional[int] = None):
        """
        Initialise le générateur

        Args:
            seed: Graine pour la reproductibilité (optionnel)
        """
        # Initialiser le générateur sécurisé
        self.secure_rng = SecureRandomGenerator(seed)

        self.parties_generees = []

    def _creer_sabot_complet(self) -> List[Carte]:
        """
        Crée un sabot complet de 416 cartes (8 decks mélangés cryptographiquement)

        Returns:
            List[Carte]: Sabot mélangé de façon cryptographiquement sécurisée
        """
        sabot = []
        couleurs = ['♠', '♥', '♦', '♣']
        rangs = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

        # Créer 8 decks complets (416 cartes exactement)
        for deck in range(DECKS_COUNT):
            for couleur in couleurs:
                for rang in rangs:
                    carte = Carte(
                        rang=rang,
                        couleur=couleur,
                        valeur=CARD_VALUES[rang]
                    )
                    sabot.append(carte)

        print(f"Sabot cree : {len(sabot)} cartes")

        # Rafraîchir l'entropie avant mélange
        self.secure_rng.refresh_entropy()

        # Mélanger le sabot avec algorithme cryptographiquement sécurisé
        sabot_melange = self.secure_rng.secure_shuffle(sabot)

        print(f"Melange cryptographiquement securise effectue")

        return sabot_melange
    
    def _effectuer_brulage(self, sabot: List[Carte]) -> Tuple[List[Carte], int, str, str]:
        """
        Effectue le brûlage selon les règles officielles du baccarat

        Args:
            sabot: Sabot complet de cartes

        Returns:
            Tuple[cartes_brulees, cards_count, parity, sync_state]
        """
        if len(sabot) == 0:
            raise ValueError("Sabot vide pour le brûlage")

        # Tirer la première carte pour déterminer le nombre de cartes à brûler
        premiere_carte = sabot.pop(0)
        cartes_brulees = [premiere_carte]

        print(f"🔥 Première carte brûlée : {premiere_carte.rang}{premiere_carte.couleur}")

        # Nombre de cartes supplémentaires à brûler selon les règles
        cartes_supplementaires = BURN_CARDS_COUNT[premiere_carte.rang]

        # Calculer le total attendu
        total_attendu = 1 + cartes_supplementaires  # 1 carte tirée + cartes supplémentaires

        print(f"🔥 Cartes supplémentaires à brûler : {cartes_supplementaires}")
        print(f"🔥 Total cartes à brûler : {total_attendu}")

        # Brûler les cartes supplémentaires
        for i in range(cartes_supplementaires):
            if len(sabot) > 0:
                carte_brulee = sabot.pop(0)
                cartes_brulees.append(carte_brulee)
                print(f"🔥 Carte brûlée {i+2}/{total_attendu} : {carte_brulee.rang}{carte_brulee.couleur}")

        # Total de cartes brûlées (vérification)
        total_brulees = len(cartes_brulees)

        # Parité du total brûlé
        burn_parity = 'PAIR' if total_brulees % 2 == 0 else 'IMPAIR'

        # État SYNC initial selon la parité (RÈGLE CORRECTE)
        # Si total brûlé PAIR → première manche commence en SYNC (0)
        # Si total brûlé IMPAIR → première manche commence en DESYNC (1)
        initial_sync = 0 if burn_parity == 'PAIR' else 1

        print(f"🔥 Parité brûlage : {burn_parity} → Première manche : {initial_sync}")

        return cartes_brulees, total_brulees, burn_parity, initial_sync
    
    def _calculer_score(self, cartes: List[Carte]) -> int:
        """
        Calcule le score d'une main selon les règles du baccarat

        Args:
            cartes: Liste des cartes de la main

        Returns:
            int: Score de la main (0-9)
        """
        total = sum(carte.valeur for carte in cartes)
        return total % 10

    def _doit_tirer_player(self, score_player: int) -> bool:
        """
        Détermine si le joueur doit tirer une 3ème carte

        Args:
            score_player: Score actuel du joueur

        Returns:
            bool: True si le joueur doit tirer
        """
        if score_player in [8, 9]:  # Naturel
            return False
        elif score_player in [0, 1, 2, 3, 4, 5]:  # Tire
            return True
        else:  # 6, 7 - Reste
            return False

    def _doit_tirer_banker(self, score_banker: int,
                          troisieme_carte_player: Optional[Carte]) -> bool:
        """
        Détermine si le banquier doit tirer une 3ème carte

        Args:
            score_banker: Score actuel du banquier
            troisieme_carte_player: 3ème carte du joueur (None si pas tirée)

        Returns:
            bool: True si le banquier doit tirer
        """
        if score_banker in [8, 9]:  # Naturel
            return False

        if troisieme_carte_player is None:  # Joueur n'a pas tiré
            if score_banker in [0, 1, 2, 3, 4, 5]:
                return True
            else:  # 6, 7
                return False
        else:  # Joueur a tiré une 3ème carte
            valeur_3eme = troisieme_carte_player.valeur

            if score_banker == 3:
                return valeur_3eme != 8
            elif score_banker == 4:
                return valeur_3eme in [2, 3, 4, 5, 6, 7]
            elif score_banker == 5:
                return valeur_3eme in [4, 5, 6, 7]
            elif score_banker == 6:
                return valeur_3eme in [6, 7]
            elif score_banker == 7:
                return False
            else:  # 0, 1, 2
                return True
    
    def _jouer_manche(self, sabot: List[Carte]) -> Tuple[List[Carte], List[Carte], int, str]:
        """
        Joue une manche complète de baccarat selon les règles officielles

        Args:
            sabot: Sabot de cartes (modifié en place)

        Returns:
            Tuple[cartes_player, cartes_banker, total_cartes, resultat]
        """
        if len(sabot) < 6:  # Sécurité - minimum 6 cartes pour une manche complète
            raise ValueError("Pas assez de cartes dans le sabot")

        # Distribution initiale : 2 cartes chacun
        cartes_player = [sabot.pop(0), sabot.pop(0)]
        cartes_banker = [sabot.pop(0), sabot.pop(0)]

        # Calcul des scores initiaux
        score_player = self._calculer_score(cartes_player)
        score_banker = self._calculer_score(cartes_banker)

        # Vérifier les naturels (8 ou 9)
        if score_player in [8, 9] or score_banker in [8, 9]:
            # Naturel - pas de 3ème carte
            pass
        else:
            # Règles de tirage
            troisieme_carte_player = None

            # Le joueur tire-t-il ?
            if self._doit_tirer_player(score_player):
                troisieme_carte_player = sabot.pop(0)
                cartes_player.append(troisieme_carte_player)
                score_player = self._calculer_score(cartes_player)

            # Le banquier tire-t-il ?
            if self._doit_tirer_banker(score_banker, troisieme_carte_player):
                cartes_banker.append(sabot.pop(0))
                score_banker = self._calculer_score(cartes_banker)

        # Déterminer le résultat
        if score_player > score_banker:
            resultat = 'PLAYER'
        elif score_banker > score_player:
            resultat = 'BANKER'
        else:
            resultat = 'TIE'

        # Total de cartes distribuées
        total_cartes = len(cartes_player) + len(cartes_banker)

        return cartes_player, cartes_banker, total_cartes, resultat

    def _calculer_nouvel_etat_sync(self, etat_actuel: int, cards_parity: str) -> int:
        """
        Calcule le nouvel état SYNC/DESYNC selon les règles Lupasco

        Args:
            etat_actuel: État SYNC/DESYNC actuel (0=SYNC, 1=DESYNC)
            cards_parity: Parité des cartes ('PAIR' ou 'IMPAIR')

        Returns:
            int: Nouvel état SYNC/DESYNC (0=SYNC, 1=DESYNC)
        """
        if cards_parity == 'PAIR':
            # PAIR conserve l'état
            return etat_actuel
        else:
            # IMPAIR change l'état
            return 1 if etat_actuel == 0 else 0
    
    def generer_partie(self, partie_number: int, max_manches: int = 60) -> PartieBaccarat:
        """
        Génère une partie complète de baccarat réaliste avec hasard cryptographiquement sécurisé

        Args:
            partie_number: Numéro de la partie
            max_manches: Nombre maximum de manches P/B (défaut: 60)

        Returns:
            PartieBaccarat: Partie générée avec mélange sécurisé
        """
        print(f"\n🎲 Génération partie {partie_number} avec hasard cryptographiquement sécurisé...")

        # Créer et mélanger le sabot de façon sécurisée
        sabot = self._creer_sabot_complet()

        # Effectuer le brûlage
        cartes_brulees, burn_cards_count, burn_parity, initial_sync = self._effectuer_brulage(sabot)

        # Créer la partie
        partie = PartieBaccarat(
            partie_number=partie_number,
            sabot_initial=sabot.copy(),  # Copie pour référence
            cartes_restantes=len(sabot),
            cartes_brulees=cartes_brulees,
            burn_cards_count=burn_cards_count,
            burn_parity=burn_parity,
            initial_sync_state=initial_sync,
            mains=[],
            current_sync_state=initial_sync
        )

        # Stocker les informations de brûlage dans la partie (pas comme une main)
        partie.premiere_carte_brulee = cartes_brulees[0] if cartes_brulees else None

        # NOUVEAU : Alignement main_number = index avec main dummy vide
        # Ajouter une main dummy complètement vide pour l'alignement
        main_dummy = MainBaccarat(
            main_number=None,
            manche_pb_number=None,
            cartes_player=[],
            cartes_banker=[],
            total_cartes_distribuees=0,
            score_player=0,
            score_banker=0,
            index1=None,
            cards_count=0,
            index2='',
            index3='',
            index5='',
            timestamp=''
        )

        # Ajouter la main dummy vide
        partie.mains.append(main_dummy)
        print(f"🎯 Main dummy vide ajoutée pour alignement main_number = index")

        # Jouer les manches jusqu'à EXACTEMENT max_manches P/B (priorité absolue)
        main_counter = 1  # Commencer à 1 (première vraie main)
        manche_pb_counter = 1  # Commencer à manche 1 (avant distribution)

        while manche_pb_counter <= max_manches:  # CONDITION PRINCIPALE : 60 manches P/B

            # Vérifier s'il reste assez de cartes pour une manche
            if len(sabot) < 6:
                print(f"ARRET FORCE : plus assez de cartes (manches P/B: {manche_pb_counter}/{max_manches})")
                break

            # Vérifier si cut card atteinte (332 cartes distribuées) - INFORMATIF SEULEMENT
            cartes_distribuees = TOTAL_CARDS - len(sabot)
            if cartes_distribuees >= CUT_CARD_POSITION and not partie.cut_card_atteinte:
                print(f"Cut card atteinte : {cartes_distribuees} cartes distribuees")
                print(f"Continuation pour atteindre {max_manches} manches P/B (manches restantes: {max_manches - manche_pb_counter})")
                partie.cut_card_atteinte = True

            try:
                # Jouer une manche
                cartes_player, cartes_banker, total_cartes, result = self._jouer_manche(sabot)

                # Calculer les scores finaux
                score_player = self._calculer_score(cartes_player)
                score_banker = self._calculer_score(cartes_banker)

                # Déterminer la catégorie INDEX2
                cards_category = CARDS_MAPPING.get(total_cartes, f"cartes_{total_cartes}")
                cards_parity = 'PAIR' if total_cartes % 2 == 0 else 'IMPAIR'

                # État SYNC au début de cette main
                current_sync = partie.current_sync_state

                # Calculer le numéro de manche P/B selon la nouvelle logique
                if result in ['PLAYER', 'BANKER']:
                    # PLAYER ou BANKER : utiliser le compteur actuel puis l'incrémenter
                    manche_pb_number = manche_pb_counter
                    manche_pb_counter += 1  # Incrémenter pour la prochaine manche P/B
                else:  # TIE
                    # TIE : utiliser le compteur actuel SANS l'incrémenter
                    manche_pb_number = manche_pb_counter

                # NOUVEAU : Créer la main avec alignement main_number = index
                # main_counter correspond maintenant directement à l'index dans le tableau
                main = MainBaccarat(
                    main_number=main_counter,  # main_counter = index de la main dans le tableau
                    manche_pb_number=manche_pb_number,
                    cartes_player=cartes_player,
                    cartes_banker=cartes_banker,
                    total_cartes_distribuees=total_cartes,
                    score_player=score_player,
                    score_banker=score_banker,
                    index1=current_sync,
                    cards_count=total_cartes,
                    index2=cards_category,
                    index3=result,
                    index5=f"{current_sync}_{cards_category}_{result}",
                    timestamp=datetime.now().isoformat()
                )

                partie.mains.append(main)

                # Mettre à jour l'état pour la prochaine main
                partie.current_sync_state = self._calculer_nouvel_etat_sync(current_sync, cards_parity)

                # Mettre à jour les statistiques (exclure la main dummy)
                partie.total_mains += 1  # Compter seulement les vraies mains
                if result in ['PLAYER', 'BANKER']:
                    partie.total_manches_pb += 1
                else:
                    partie.total_ties += 1

                main_counter += 1
                partie.cartes_restantes = len(sabot)

                # Vérifier si on a atteint exactement max_manches P/B
                if manche_pb_counter > max_manches:
                    print(f"Objectif atteint : {max_manches} manches P/B completees")
                    break

            except ValueError as e:
                # Plus assez de cartes
                print(f"Fin de partie forcee : {e}")
                print(f"Manches P/B realisees : {manche_pb_counter}/{max_manches}")
                break

        # Afficher le résumé de la partie
        manches_realisees = manche_pb_counter - 1  # -1 car le compteur est toujours en avance
        if manches_realisees == max_manches:
            print(f"Partie {partie_number} terminee : {max_manches} manches P/B (objectif atteint)")
        else:
            print(f"Partie {partie_number} terminee : {manches_realisees}/{max_manches} manches P/B")

        return partie
    
    def generer_multiple_parties(self, nb_parties: int, max_manches: int = 60) -> List[PartieBaccarat]:
        """
        Génère plusieurs parties
        
        Args:
            nb_parties: Nombre de parties à générer
            max_manches: Nombre maximum de manches P/B par partie
        
        Returns:
            List[PartieBaccarat]: Liste des parties générées
        """
        parties = []
        
        for i in range(1, nb_parties + 1):
            partie = self.generer_partie(i, max_manches)
            parties.append(partie)
            self.parties_generees.append(partie)
            
            # Affichage détaillé du résultat
            if partie.total_manches_pb == max_manches:
                print(f"✅ Partie {i}/{nb_parties} - {partie.total_manches_pb} manches P/B, {partie.total_ties} TIE (OBJECTIF ATTEINT)")
            else:
                print(f"⚠️  Partie {i}/{nb_parties} - {partie.total_manches_pb} manches P/B, {partie.total_ties} TIE (INCOMPLET)")
                if partie.cut_card_atteinte:
                    print(f"   → Cut card atteinte, cartes insuffisantes")
        
        return parties
    
    def exporter_txt(self, parties: List[PartieBaccarat], filename: str):
        """
        Exporte les parties en format texte (.txt) selon le format demandé

        Args:
            parties: Liste des parties à exporter
            filename: Nom du fichier TXT
        """
        with open(filename, 'w', encoding='utf-8') as txtfile:
            # En-tête du fichier
            txtfile.write("GÉNÉRATEUR PARTIES BACCARAT LUPASCO\n")
            txtfile.write("=" * 50 + "\n")
            txtfile.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txtfile.write(f"Nombre de parties : {len(parties)}\n")
            txtfile.write("Hasard cryptographiquement sécurisé\n")
            txtfile.write("=" * 50 + "\n\n")

            for partie in parties:
                # Informations de brûlage en en-tête de chaque partie
                premiere_carte = partie.premiere_carte_brulee
                carte_str = f"{premiere_carte.rang}{premiere_carte.couleur}" if premiere_carte else "N/A"

                txtfile.write(f"PARTIE {partie.partie_number}\n")
                txtfile.write(f"BURN: {partie.burn_cards_count} cartes | Première carte: {carte_str} | INDEX1 initial: {partie.initial_sync_state}\n")
                txtfile.write("-" * 70 + "\n")
                txtfile.write("Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5\n")
                txtfile.write("-" * 55 + "\n")

                for main in partie.mains:
                    # NOUVEAU : Exclure la main dummy (main_number = None) de l'export TXT
                    if main.main_number is None:
                        continue  # Ignorer la main dummy dans l'export

                    # Formater le numéro de manche
                    manche_str = str(main.manche_pb_number) if main.manche_pb_number is not None else "-"

                    # Ligne de données
                    ligne = f"{main.main_number:4d} | {manche_str:6s} | {main.index1:6d} | {main.index2:8s} | {main.index3:6s} | {main.index5}"
                    txtfile.write(ligne + "\n")

                txtfile.write("\n")  # Ligne vide entre parties

        print(f"Export TXT terminé : {filename}")

    def exporter_json(self, parties: List[PartieBaccarat], filename: str):
        """
        Exporte les parties en format JSON pour analyse facile

        Args:
            parties: Liste des parties à exporter
            filename: Nom du fichier JSON
        """
        def carte_to_dict(carte: Carte) -> Dict:
            """Convertit une carte en dictionnaire"""
            return {
                "rang": carte.rang,
                "couleur": carte.couleur,
                "valeur": carte.valeur
            }

        def main_to_dict(main: MainBaccarat) -> Dict:
            """Convertit une main en dictionnaire"""
            return {
                "main_number": main.main_number,
                "manche_pb_number": main.manche_pb_number,
                "cartes_player": [carte_to_dict(c) for c in main.cartes_player],
                "cartes_banker": [carte_to_dict(c) for c in main.cartes_banker],
                "total_cartes_distribuees": main.total_cartes_distribuees,
                "score_player": main.score_player,
                "score_banker": main.score_banker,
                "index1": main.index1,
                "cards_count": main.cards_count,
                "index2": main.index2,
                "index3": main.index3,
                "index5": main.index5,
                "timestamp": main.timestamp
            }

        def partie_to_dict(partie: PartieBaccarat) -> Dict:
            """Convertit une partie en dictionnaire"""
            return {
                "partie_number": partie.partie_number,
                "burn_info": {
                    "cartes_brulees": [carte_to_dict(c) for c in partie.cartes_brulees],
                    "burn_cards_count": partie.burn_cards_count,
                    "burn_parity": partie.burn_parity,
                    "initial_sync_state": partie.initial_sync_state,
                    "premiere_carte_brulee": carte_to_dict(partie.premiere_carte_brulee) if partie.premiere_carte_brulee else None
                },
                "statistiques": {
                    "total_mains": partie.total_mains,
                    "total_manches_pb": partie.total_manches_pb,
                    "total_ties": partie.total_ties,
                    "cut_card_atteinte": partie.cut_card_atteinte,
                    "cartes_restantes": partie.cartes_restantes
                },
                "mains": [main_to_dict(main) for main in partie.mains]
            }

        # Structure JSON principale
        data = {
            "metadata": {
                "generateur": "GÉNÉRATEUR PARTIES BACCARAT LUPASCO",
                "version": "2.0",
                "date_generation": datetime.now().isoformat(),
                "nombre_parties": len(parties),
                "hasard_cryptographique": True,
                "description": "Parties de baccarat générées avec hasard cryptographiquement sécurisé selon les règles Lupasco"
            },
            "configuration": {
                "decks_count": DECKS_COUNT,
                "total_cards": TOTAL_CARDS,
                "cut_card_position": CUT_CARD_POSITION,
                "cards_mapping": CARDS_MAPPING
            },
            "parties": [partie_to_dict(partie) for partie in parties]
        }

        # Écrire le fichier JSON avec indentation pour lisibilité
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, indent=2, ensure_ascii=False)

        print(f"Export JSON terminé : {filename}")

# ============================================================================
# EXEMPLE D'UTILISATION
# ============================================================================

def generer_dataset_complet():
    """Génère 1000 parties complètes pour l'analyse Lupasco avec hasard cryptographiquement sécurisé"""
    print("🎲 GÉNÉRATEUR DE PARTIES BACCARAT LUPASCO")
    print("🔐 AVEC HASARD CRYPTOGRAPHIQUEMENT SÉCURISÉ")
    print("📊 GÉNÉRATION DE 1000 PARTIES COMPLÈTES")
    print("=" * 60)

    # Créer le générateur avec hasard sécurisé
    generateur = GenerateurPartiesBaccarat()

    # Générer 1000 parties complètes (60 manches chacune)
    print("Génération de 1000 parties complètes (60 manches P/B chacune)...")
    print("⚠️  Cette opération peut prendre plusieurs minutes...")
    parties = generateur.generer_multiple_parties(nb_parties=1000, max_manches=60)

    # Créer les noms de fichiers avec timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename_txt = f"dataset_baccarat_lupasco_{timestamp}.txt"
    filename_json = f"dataset_baccarat_lupasco_{timestamp}.json"

    # Exporter en TXT
    print("📝 Export en cours vers fichier TXT...")
    generateur.exporter_txt(parties, filename_txt)

    # Exporter en JSON
    print("📋 Export en cours vers fichier JSON...")
    generateur.exporter_json(parties, filename_json)

    # Calculer les statistiques détaillées
    print("\n📊 STATISTIQUES DÉTAILLÉES :")
    total_mains = sum(p.total_mains for p in parties)
    total_pb = sum(p.total_manches_pb for p in parties)
    total_ties = sum(p.total_ties for p in parties)

    # Compter les parties complètes (60 manches P/B)
    parties_completes = sum(1 for p in parties if p.total_manches_pb == 60)
    parties_incompletes = len(parties) - parties_completes

    # Compter les résultats
    player_count = 0
    banker_count = 0
    tie_count = 0

    # Compter les cartes
    cards_4_count = 0
    cards_5_count = 0
    cards_6_count = 0

    # Compter les états SYNC/DESYNC
    sync_count = 0
    desync_count = 0

    for partie in parties:
        for main in partie.mains:
            # NOUVEAU : Exclure la main dummy (main_number = X) des statistiques
            if main.main_number == 'X':
                continue  # Ignorer la main dummy

            # Résultats
            if main.index3 == 'PLAYER':
                player_count += 1
            elif main.index3 == 'BANKER':
                banker_count += 1
            elif main.index3 == 'TIE':
                tie_count += 1

            # Cartes
            if main.cards_count == 4:
                cards_4_count += 1
            elif main.cards_count == 5:
                cards_5_count += 1
            elif main.cards_count == 6:
                cards_6_count += 1

            # États SYNC/DESYNC
            if main.index1 == 0:  # SYNC
                sync_count += 1
            else:  # DESYNC
                desync_count += 1

    print(f"Total parties générées : {len(parties)}")
    print(f"Parties complètes (60 manches P/B) : {parties_completes}")
    print(f"Parties incomplètes : {parties_incompletes}")
    print(f"Total mains générées : {total_mains}")
    print(f"Total manches P/B : {total_pb}")
    print(f"Total TIE : {total_ties}")
    print(f"Taux de réussite : {parties_completes/len(parties)*100:.1f}%")
    print()
    print("RÉPARTITION DES RÉSULTATS :")
    print(f"PLAYER : {player_count} ({player_count/total_mains*100:.2f}%)")
    print(f"BANKER : {banker_count} ({banker_count/total_mains*100:.2f}%)")
    print(f"TIE : {tie_count} ({tie_count/total_mains*100:.2f}%)")
    print()
    print("RÉPARTITION DES CARTES :")
    print(f"4 cartes : {cards_4_count} ({cards_4_count/total_mains*100:.2f}%)")
    print(f"5 cartes : {cards_5_count} ({cards_5_count/total_mains*100:.2f}%)")
    print(f"6 cartes : {cards_6_count} ({cards_6_count/total_mains*100:.2f}%)")
    print()
    print("RÉPARTITION SYNC/DESYNC :")
    print(f"SYNC : {sync_count} ({sync_count/total_mains*100:.2f}%)")
    print(f"DESYNC : {desync_count} ({desync_count/total_mains*100:.2f}%)")

    print(f"\n✅ Dataset généré avec succès !")
    print(f"📝 Fichier TXT : {filename_txt}")
    print(f"📋 Fichier JSON : {filename_json}")
    print(f"💾 100000 parties complètes générées avec hasard cryptographiquement sécurisé")

    return filename_txt, filename_json

def test_generateur():
    """Test simple du générateur pour vérifier la logique et les exports"""
    print("TEST DU GENERATEUR BACCARAT LUPASCO")
    print("=" * 50)

    # Créer le générateur
    generateur = GenerateurPartiesBaccarat(seed=42)  # Seed pour reproductibilité

    # Générer 1 partie complète (60 manches P/B)
    print("Génération d'une partie complète (60 manches P/B)...")
    partie = generateur.generer_partie(100, max_manches=60)

    # Afficher les résultats détaillés
    print(f"\nRESULTATS DE LA PARTIE TEST :")
    print(f"Brûlage : {partie.burn_cards_count} cartes, parité {partie.burn_parity}")
    print(f"État initial : {partie.initial_sync_state}")
    print(f"Total mains : {len(partie.mains)}")
    print(f"Manches P/B : {partie.total_manches_pb}")
    print(f"TIE : {partie.total_ties}")

    # Afficher les informations de brûlage
    premiere_carte = partie.premiere_carte_brulee
    carte_str = f"{premiere_carte.rang}{premiere_carte.couleur}" if premiere_carte else "N/A"
    print(f"BURN: {partie.burn_cards_count} cartes | Première carte: {carte_str} | INDEX1 initial: {partie.initial_sync_state}")

    print(f"\nDETAIL DES PREMIERES MAINS :")
    print("Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5")
    print("-" * 55)

    for i, main in enumerate(partie.mains[:15]):  # Afficher les 15 premières mains
        manche_str = str(main.manche_pb_number) if main.manche_pb_number is not None else "-"
        print(f"{main.main_number:4d} | {manche_str:6s} | {main.index1:6d} | {main.index2:8s} | {main.index3:6s} | {main.index5}")

    # Test des exports TXT et JSON
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename_txt = f"test_partie_lupasco_{timestamp}.txt"
    filename_json = f"test_partie_lupasco_{timestamp}.json"

    print(f"\nTEST DES EXPORTS :")
    # generateur.exporter_txt([partie], filename_txt)  # DÉSACTIVÉ
    generateur.exporter_json([partie], filename_json)

    print(f"✅ Fichiers de test générés :")
    # print(f"📝 TXT : {filename_txt}")  # DÉSACTIVÉ
    print(f"📋 JSON : {filename_json}")

    return partie

# ============================================================================
# ANALYSEUR SCIENTIFIQUE RÉVOLUTIONNAIRE BACCARAT LUPASCO
# ============================================================================

class AnalyseurScientifiqueRevolutionnaire:
    """
    ANALYSEUR SCIENTIFIQUE RÉVOLUTIONNAIRE BACCARAT LUPASCO
    ======================================================

    Implémente les 10 améliorations prioritaires identifiées dans ref.txt :

    1. Tests d'hypothèses optimaux (Stein's lemma, Large deviation theory)
    2. Estimation spectrale par entropie maximale (Burg's theorem)
    3. Modèles de Markov cachés avancés
    4. Analyse de séquences conjointement typiques (AEP)
    5. Complexité de Kolmogorov et universalité
    6. Analyse harmonique et transformées de Fourier
    7. Transformations de séries (Kummer, Euler)
    8. Information de Fisher et efficacité statistique
    9. Fonctions spéciales pour modélisation précise
    10. Estimation par entropie maximale généralisée

    OBJECTIFS RÉVOLUTIONNAIRES :
    - Détecter mémoire systémique et auto-régulation
    - Quantifier corrélations séquentielles
    - Identifier mécanismes d'équilibrage/homéostasie
    - Mesurer prédictibilité partielle
    - Prouver système complexe adaptatif

    QUANTIFICATIONS CIBLES :
    - Équilibre SYNC/DESYNC impossible en hasard pur (p-value < 10^-8)
    - Constance universelle dans toutes sous-catégories
    - Mécanismes de compensation A/B/C
    - Structure fractale à tous niveaux
    - Entropie contrôlée maintenant ordre et désordre
    """

    def __init__(self, dataset_path: str = None, nb_parties_analyse: int = 100):
        """
        Initialise l'analyseur révolutionnaire

        Args:
            dataset_path: Chemin vers le dataset JSON baccarat Lupasco
            nb_parties_analyse: Nombre de parties à analyser (défaut: 100)
        """
        self.dataset_path = dataset_path
        self.nb_parties_analyse = nb_parties_analyse
        self.data = None
        self.sequences = {}
        self.resultats = {}

        print(f"🔬 ANALYSEUR SCIENTIFIQUE RÉVOLUTIONNAIRE BACCARAT LUPASCO")
        print(f"📊 Configuration : {nb_parties_analyse} parties à analyser")
        print(f"🎯 Objectif : Détecter et quantifier le système complexe organisé")
        print(f"=" * 80)

    def charger_dataset(self) -> bool:
        """
        Charge le dataset JSON et extrait les séquences pour analyse

        Returns:
            bool: True si chargement réussi
        """
        if not self.dataset_path:
            print("❌ Aucun dataset spécifié")
            return False

        try:
            print(f"📂 Chargement du dataset : {self.dataset_path}")

            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)

            # Extraire les informations du dataset
            nb_parties_total = len(self.data['parties'])
            print(f"📊 Dataset chargé : {nb_parties_total} parties disponibles")

            # Limiter à nb_parties_analyse
            parties_a_analyser = min(self.nb_parties_analyse, nb_parties_total)
            print(f"🎯 Analyse configurée pour {parties_a_analyser} parties")

            # Extraire les séquences des parties sélectionnées
            self._extraire_sequences(parties_a_analyser)

            return True

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False

    def _extraire_sequences(self, nb_parties: int):
        """
        Extrait toutes les séquences nécessaires pour l'analyse révolutionnaire

        Args:
            nb_parties: Nombre de parties à traiter
        """
        print(f"🔍 Extraction des séquences pour analyse révolutionnaire...")

        # Initialiser les listes de séquences
        index1_seq = []  # SYNC/DESYNC
        index2_seq = []  # A/B/C
        index3_seq = []  # PLAYER/BANKER/TIE
        index5_seq = []  # INDEX5 complet
        cards_count_seq = []  # Nombre de cartes

        mains_valides = 0

        for i, partie in enumerate(self.data['parties'][:nb_parties]):
            for main in partie['mains']:
                # Exclure les mains dummy (main_number = None)
                if main['main_number'] is None:
                    continue

                # Exclure les mains de brûlage (index3 vide)
                if not main['index3']:
                    continue

                # Ajouter aux séquences
                index1_seq.append(main['index1'])
                index2_seq.append(main['index2'])
                index3_seq.append(main['index3'])
                index5_seq.append(main['index5'])
                cards_count_seq.append(main['cards_count'])

                mains_valides += 1

        # Stocker les séquences
        self.sequences = {
            'index1': np.array(index1_seq),
            'index2': np.array(index2_seq),
            'index3': np.array(index3_seq),
            'index5': np.array(index5_seq),
            'cards_count': np.array(cards_count_seq)
        }

        print(f"✅ Séquences extraites : {mains_valides} mains valides")
        print(f"📈 INDEX1 (SYNC/DESYNC) : {len(self.sequences['index1'])} valeurs")
        print(f"📈 INDEX2 (A/B/C) : {len(self.sequences['index2'])} valeurs")
        print(f"📈 INDEX3 (P/B/T) : {len(self.sequences['index3'])} valeurs")
        print(f"📈 INDEX5 (complet) : {len(self.sequences['index5'])} valeurs")

    def analyser_systeme_complexe_complet(self) -> Dict:
        """
        ANALYSE RÉVOLUTIONNAIRE COMPLÈTE DU SYSTÈME COMPLEXE BACCARAT LUPASCO

        Implémente les 10 améliorations prioritaires pour détecter et quantifier :
        - Mémoire systémique et auto-régulation
        - Corrélations séquentielles
        - Mécanismes d'équilibrage/homéostasie
        - Prédictibilité partielle
        - Système complexe adaptatif

        Returns:
            Dict: Résultats complets de l'analyse révolutionnaire
        """
        print(f"\n🚀 DÉBUT DE L'ANALYSE RÉVOLUTIONNAIRE COMPLÈTE")
        print(f"🔬 Application des 10 améliorations prioritaires")
        print(f"=" * 80)

        resultats = {}

        # 1. TESTS D'HYPOTHÈSES OPTIMAUX (Stein's lemma, Large deviation theory)
        print(f"\n1️⃣ TESTS D'HYPOTHÈSES OPTIMAUX")
        resultats['tests_optimaux'] = self._tests_hypotheses_optimaux()

        # 2. ESTIMATION SPECTRALE PAR ENTROPIE MAXIMALE (Burg's theorem)
        print(f"\n2️⃣ ESTIMATION SPECTRALE PAR ENTROPIE MAXIMALE")
        resultats['analyse_spectrale'] = self._estimation_spectrale_entropie_maximale()

        # 3. MODÈLES DE MARKOV CACHÉS AVANCÉS
        print(f"\n3️⃣ MODÈLES DE MARKOV CACHÉS AVANCÉS")
        resultats['markov_caches'] = self._modeliser_markov_caches_avances()

        # 4. ANALYSE DE SÉQUENCES CONJOINTEMENT TYPIQUES (AEP)
        print(f"\n4️⃣ SÉQUENCES CONJOINTEMENT TYPIQUES")
        resultats['sequences_typiques'] = self._analyser_sequences_conjointement_typiques()

        # 5. COMPLEXITÉ DE KOLMOGOROV ET UNIVERSALITÉ
        print(f"\n5️⃣ COMPLEXITÉ DE KOLMOGOROV")
        resultats['complexite_kolmogorov'] = self._analyser_complexite_kolmogorov()

        # 6. ANALYSE HARMONIQUE ET TRANSFORMÉES DE FOURIER
        print(f"\n6️⃣ ANALYSE HARMONIQUE AVANCÉE")
        resultats['analyse_harmonique'] = self._analyser_harmoniques_avances()

        # 7. TRANSFORMATIONS DE SÉRIES (Kummer, Euler)
        print(f"\n7️⃣ TRANSFORMATIONS DE SÉRIES")
        resultats['transformations_series'] = self._appliquer_transformations_series()

        # 8. INFORMATION DE FISHER ET EFFICACITÉ STATISTIQUE
        print(f"\n8️⃣ INFORMATION DE FISHER")
        resultats['information_fisher'] = self._analyser_information_fisher()

        # 9. FONCTIONS SPÉCIALES POUR MODÉLISATION PRÉCISE
        print(f"\n9️⃣ FONCTIONS SPÉCIALES")
        resultats['fonctions_speciales'] = self._utiliser_fonctions_speciales()

        # 10. ESTIMATION PAR ENTROPIE MAXIMALE GÉNÉRALISÉE
        print(f"\n🔟 ENTROPIE MAXIMALE GÉNÉRALISÉE")
        resultats['entropie_maximale'] = self._estimation_entropie_maximale_generalisee()

        # SYNTHÈSE RÉVOLUTIONNAIRE
        print(f"\n🎯 SYNTHÈSE RÉVOLUTIONNAIRE")
        resultats['synthese'] = self._synthese_revolutionnaire(resultats)

        self.resultats = resultats
        return resultats

    def _tests_hypotheses_optimaux(self) -> Dict:
        """
        TESTS D'HYPOTHÈSES OPTIMAUX basés sur Stein's lemma et Large deviation theory

        Teste H0: hasard pur vs H1: système complexe Lupasco
        Calcule les exposants d'erreur optimaux
        Applique la théorie des grandes déviations

        Returns:
            Dict: Résultats des tests optimaux
        """
        print("🧪 Tests d'hypothèses optimaux (Stein's lemma)")

        resultats = {}

        # Test 1: Équilibre SYNC/DESYNC impossible en hasard pur
        sync_count = np.sum(self.sequences['index1'] == 0)
        desync_count = np.sum(self.sequences['index1'] == 1)
        total = len(self.sequences['index1'])

        # Test binomial exact pour équilibre parfait
        p_observed = sync_count / total

        # Calcul de la log-vraisemblance ratio (Stein's lemma)
        if p_observed > 0 and p_observed < 1:
            log_likelihood_ratio = (sync_count * np.log(p_observed / 0.5) +
                                  desync_count * np.log((1-p_observed) / 0.5))
        else:
            log_likelihood_ratio = np.inf

        # Test exact binomial
        p_value_exact = stats.binom_test(sync_count, total, 0.5)

        # Large deviation theory : calcul de l'exposant d'erreur
        deviation = abs(p_observed - 0.5)
        if deviation > 0:
            # Fonction de taux de Cramér pour distribution binomiale
            def rate_function(x):
                if x <= 0 or x >= 1:
                    return np.inf
                return x * np.log(x / 0.5) + (1-x) * np.log((1-x) / 0.5)

            exposant_erreur = total * rate_function(p_observed)
        else:
            exposant_erreur = 0

        resultats['equilibre_sync_desync'] = {
            'sync_count': sync_count,
            'desync_count': desync_count,
            'proportion_sync': p_observed,
            'p_value_exact': p_value_exact,
            'log_likelihood_ratio': log_likelihood_ratio,
            'exposant_erreur': exposant_erreur,
            'significatif': p_value_exact < 1e-8,
            'interpretation': 'SYSTÈME COMPLEXE DÉTECTÉ' if p_value_exact < 1e-8 else 'ÉQUILIBRE POSSIBLE'
        }

        # Test 2: Constance universelle dans sous-catégories
        sous_categories = {}
        for cat in ['A', 'B', 'C']:
            mask = self.sequences['index2'] == cat
            if np.sum(mask) > 0:
                sync_cat = np.sum((self.sequences['index1'] == 0) & mask)
                total_cat = np.sum(mask)
                p_cat = sync_cat / total_cat
                p_val_cat = stats.binom_test(sync_cat, total_cat, 0.5)

                sous_categories[cat] = {
                    'sync_count': sync_cat,
                    'total': total_cat,
                    'proportion': p_cat,
                    'p_value': p_val_cat,
                    'deviation_from_global': abs(p_cat - p_observed)
                }

        resultats['constance_universelle'] = sous_categories

        # Test 3: Chernoff bound pour probabilités d'erreur
        # Borne de Chernoff pour la probabilité d'erreur de type I
        if deviation > 0:
            chernoff_bound = np.exp(-total * rate_function(p_observed))
        else:
            chernoff_bound = 1.0

        resultats['chernoff_bound'] = chernoff_bound

        print(f"   ✅ Équilibre SYNC/DESYNC : p-value = {p_value_exact:.2e}")
        print(f"   ✅ Exposant d'erreur : {exposant_erreur:.2f}")
        print(f"   ✅ Borne de Chernoff : {chernoff_bound:.2e}")

        return resultats

    def _estimation_spectrale_entropie_maximale(self) -> Dict:
        """
        ESTIMATION SPECTRALE PAR ENTROPIE MAXIMALE (Burg's theorem)

        Détecte les cycles cachés dans les séquences INDEX5
        Implémente Burg's maximum entropy theorem
        Analyse des harmoniques dans les corrélations SYNC/DESYNC

        Returns:
            Dict: Résultats de l'analyse spectrale avancée
        """
        print("📊 Estimation spectrale par entropie maximale (Burg)")

        resultats = {}

        # Analyse spectrale de la séquence INDEX1 (SYNC/DESYNC)
        index1_numeric = self.sequences['index1'].astype(float)

        # Méthode de Burg pour estimation spectrale par entropie maximale
        def burg_method(x, order=20):
            """Implémentation de la méthode de Burg"""
            N = len(x)

            # Initialisation
            ak = np.zeros(order + 1)
            ak[0] = 1.0

            # Variables de travail
            f = x.copy()
            b = x.copy()

            for k in range(order):
                # Calcul du coefficient de réflexion
                num = -2 * np.sum(f[k+1:] * b[:-k-1])
                den = np.sum(f[k+1:]**2) + np.sum(b[:-k-1]**2)

                if den == 0:
                    break

                reflection_coeff = num / den

                # Mise à jour des coefficients
                ak_new = ak.copy()
                for i in range(k+1):
                    ak_new[i] += reflection_coeff * ak[k-i]
                ak_new[k+1] = reflection_coeff
                ak = ak_new

                # Mise à jour des erreurs de prédiction
                f_new = f[1:] + reflection_coeff * b[:-1]
                b_new = b[:-1] + reflection_coeff * f[1:]
                f = f_new[k:]
                b = b_new

            return ak

        # Application de la méthode de Burg
        try:
            coeffs_burg = burg_method(index1_numeric, order=min(20, len(index1_numeric)//4))

            # Calcul de la densité spectrale de puissance
            freqs = np.linspace(0, 0.5, 1000)
            psd_burg = np.zeros_like(freqs)

            for i, f in enumerate(freqs):
                z = np.exp(-2j * np.pi * f)
                denominator = np.polyval(coeffs_burg, z)
                psd_burg[i] = 1.0 / (np.abs(denominator)**2)

            # Normalisation
            psd_burg = psd_burg / np.max(psd_burg)

            # Détection des pics spectraux
            peaks, properties = signal.find_peaks(psd_burg, height=0.1, distance=10)

            cycles_detectes = []
            for peak in peaks:
                freq = freqs[peak]
                periode = 1.0 / freq if freq > 0 else np.inf
                amplitude = psd_burg[peak]

                cycles_detectes.append({
                    'frequence': freq,
                    'periode': periode,
                    'amplitude': amplitude
                })

            resultats['burg_analysis'] = {
                'coefficients': coeffs_burg.tolist(),
                'frequences': freqs.tolist(),
                'psd': psd_burg.tolist(),
                'cycles_detectes': cycles_detectes,
                'nb_cycles': len(cycles_detectes)
            }

        except Exception as e:
            print(f"   ⚠️ Erreur dans l'analyse de Burg : {e}")
            resultats['burg_analysis'] = {'erreur': str(e)}

        # Analyse FFT classique pour comparaison
        fft_result = fft(index1_numeric)
        freqs_fft = fftfreq(len(index1_numeric))
        psd_fft = np.abs(fft_result)**2

        # Garder seulement les fréquences positives
        positive_freqs = freqs_fft[:len(freqs_fft)//2]
        psd_positive = psd_fft[:len(psd_fft)//2]

        resultats['fft_analysis'] = {
            'frequences': positive_freqs.tolist(),
            'psd': psd_positive.tolist()
        }

        print(f"   ✅ Méthode de Burg appliquée")
        print(f"   ✅ {len(cycles_detectes)} cycles détectés")

        return resultats

    def _modeliser_markov_caches_avances(self) -> Dict:
        """
        MODÈLES DE MARKOV CACHÉS AVANCÉS

        Modélise les états latents du système baccarat Lupasco
        États cachés : SYNC_DOMINANT, DESYNC_DOMINANT, EQUILIBRE
        Calcule les taux d'entropie conditionnels

        Returns:
            Dict: Résultats de la modélisation Markov cachée
        """
        print("🔗 Modèles de Markov cachés avancés")

        resultats = {}

        # Analyse des transitions INDEX1 (SYNC/DESYNC)
        index1_seq = self.sequences['index1']

        # Matrice de transition observée
        transitions = np.zeros((2, 2))  # 2x2 pour SYNC/DESYNC

        for i in range(len(index1_seq) - 1):
            current_state = index1_seq[i]
            next_state = index1_seq[i + 1]
            transitions[current_state, next_state] += 1

        # Normalisation pour obtenir les probabilités
        transition_probs = transitions / transitions.sum(axis=1, keepdims=True)

        # Calcul de la distribution stationnaire
        eigenvals, eigenvecs = np.linalg.eig(transition_probs.T)
        stationary_idx = np.argmax(np.real(eigenvals))
        stationary_dist = np.real(eigenvecs[:, stationary_idx])
        stationary_dist = stationary_dist / stationary_dist.sum()

        # Entropie de la chaîne de Markov
        entropy_rate = 0
        for i in range(2):
            for j in range(2):
                if transition_probs[i, j] > 0:
                    entropy_rate -= (stationary_dist[i] * transition_probs[i, j] *
                                   np.log2(transition_probs[i, j]))

        # Test de mémoire markovienne (test du chi-2)
        # Comparaison avec modèle indépendant
        expected_transitions = np.outer(stationary_dist, stationary_dist) * transitions.sum()
        chi2_stat = np.sum((transitions - expected_transitions)**2 / expected_transitions)
        p_value_markov = 1 - stats.chi2.cdf(chi2_stat, df=1)

        resultats['markov_simple'] = {
            'matrice_transition': transition_probs.tolist(),
            'distribution_stationnaire': stationary_dist.tolist(),
            'taux_entropie': entropy_rate,
            'chi2_independance': chi2_stat,
            'p_value_independance': p_value_markov,
            'memoire_detectee': p_value_markov < 0.05
        }

        # Modèle de Markov d'ordre supérieur (ordre 2)
        if len(index1_seq) >= 3:
            # États d'ordre 2 : (état_t-1, état_t)
            states_order2 = []
            for i in range(len(index1_seq) - 1):
                state = (index1_seq[i], index1_seq[i + 1])
                states_order2.append(state)

            # Transitions d'ordre 2
            transitions_order2 = defaultdict(lambda: defaultdict(int))
            for i in range(len(states_order2) - 1):
                current_state = states_order2[i]
                next_value = index1_seq[i + 2]
                transitions_order2[current_state][next_value] += 1

            # Calcul des probabilités conditionnelles
            conditional_probs = {}
            for state, next_counts in transitions_order2.items():
                total = sum(next_counts.values())
                if total > 0:
                    conditional_probs[state] = {next_val: count/total
                                              for next_val, count in next_counts.items()}

            resultats['markov_ordre2'] = {
                'transitions': dict(transitions_order2),
                'probabilites_conditionnelles': conditional_probs,
                'nb_etats': len(conditional_probs)
            }

        # Analyse des patterns de longueur variable
        def analyser_patterns(sequence, max_length=5):
            """Analyse les patterns de longueur variable"""
            patterns = defaultdict(int)

            for length in range(1, min(max_length + 1, len(sequence))):
                for i in range(len(sequence) - length + 1):
                    pattern = tuple(sequence[i:i + length])
                    patterns[pattern] += 1

            return patterns

        patterns = analyser_patterns(index1_seq)

        # Sélectionner les patterns les plus fréquents
        patterns_frequents = dict(sorted(patterns.items(),
                                       key=lambda x: x[1], reverse=True)[:20])

        resultats['patterns_frequents'] = {
            str(pattern): count for pattern, count in patterns_frequents.items()
        }

        print(f"   ✅ Taux d'entropie Markov : {entropy_rate:.4f} bits")
        print(f"   ✅ Mémoire détectée : {p_value_markov < 0.05}")
        print(f"   ✅ {len(patterns_frequents)} patterns fréquents identifiés")

        return resultats

    def _analyser_sequences_conjointement_typiques(self) -> Dict:
        """
        ANALYSE DE SÉQUENCES CONJOINTEMENT TYPIQUES (AEP)

        Détecte les patterns dans l'espace joint (INDEX1, INDEX2, INDEX3)
        Quantifie l'information mutuelle multi-variée
        Applique l'Asymptotic Equipartition Property

        Returns:
            Dict: Résultats de l'analyse des séquences typiques
        """
        print("🎯 Séquences conjointement typiques (AEP)")

        resultats = {}

        # Création des séquences jointes
        index1_seq = self.sequences['index1']
        index2_seq = self.sequences['index2']
        index3_seq = self.sequences['index3']

        # Séquences jointes de différentes dimensions
        joint_12 = list(zip(index1_seq, index2_seq))
        joint_13 = list(zip(index1_seq, index3_seq))
        joint_23 = list(zip(index2_seq, index3_seq))
        joint_123 = list(zip(index1_seq, index2_seq, index3_seq))

        # Calcul des entropies
        def calculer_entropie(sequence):
            """Calcule l'entropie empirique d'une séquence"""
            counts = Counter(sequence)
            total = len(sequence)
            entropy = 0
            for count in counts.values():
                p = count / total
                if p > 0:
                    entropy -= p * np.log2(p)
            return entropy

        # Entropies marginales
        H_1 = calculer_entropie(index1_seq)
        H_2 = calculer_entropie(index2_seq)
        H_3 = calculer_entropie(index3_seq)

        # Entropies jointes
        H_12 = calculer_entropie(joint_12)
        H_13 = calculer_entropie(joint_13)
        H_23 = calculer_entropie(joint_23)
        H_123 = calculer_entropie(joint_123)

        # Informations mutuelles
        I_12 = H_1 + H_2 - H_12  # I(INDEX1; INDEX2)
        I_13 = H_1 + H_3 - H_13  # I(INDEX1; INDEX3)
        I_23 = H_2 + H_3 - H_23  # I(INDEX2; INDEX3)

        # Information mutuelle conditionnelle
        I_13_given_2 = H_13 + H_2 - H_123 - H_2  # I(INDEX1; INDEX3 | INDEX2)

        # Information mutuelle totale (interaction d'ordre 3)
        I_123 = H_1 + H_2 + H_3 - H_12 - H_13 - H_23 + H_123

        resultats['entropies'] = {
            'H_INDEX1': H_1,
            'H_INDEX2': H_2,
            'H_INDEX3': H_3,
            'H_INDEX1_INDEX2': H_12,
            'H_INDEX1_INDEX3': H_13,
            'H_INDEX2_INDEX3': H_23,
            'H_INDEX1_INDEX2_INDEX3': H_123
        }

        resultats['informations_mutuelles'] = {
            'I_INDEX1_INDEX2': I_12,
            'I_INDEX1_INDEX3': I_13,
            'I_INDEX2_INDEX3': I_23,
            'I_INDEX1_INDEX3_given_INDEX2': I_13_given_2,
            'I_interaction_ordre3': I_123
        }

        # Test d'indépendance basé sur l'information mutuelle
        # Utilisation du test G (likelihood ratio test)
        def test_independance_G(joint_seq, marg1_seq, marg2_seq):
            """Test G d'indépendance"""
            joint_counts = Counter(joint_seq)
            marg1_counts = Counter(marg1_seq)
            marg2_counts = Counter(marg2_seq)

            total = len(joint_seq)
            G_stat = 0

            for (val1, val2), observed in joint_counts.items():
                expected = (marg1_counts[val1] * marg2_counts[val2]) / total
                if expected > 0:
                    G_stat += 2 * observed * np.log(observed / expected)

            # Degrés de liberté
            df = (len(marg1_counts) - 1) * (len(marg2_counts) - 1)
            p_value = 1 - stats.chi2.cdf(G_stat, df) if df > 0 else 1.0

            return G_stat, p_value

        # Tests d'indépendance
        G_12, p_12 = test_independance_G(joint_12, index1_seq, index2_seq)
        G_13, p_13 = test_independance_G(joint_13, index1_seq, index3_seq)
        G_23, p_23 = test_independance_G(joint_23, index2_seq, index3_seq)

        resultats['tests_independance'] = {
            'INDEX1_INDEX2': {'G_stat': G_12, 'p_value': p_12, 'independant': p_12 > 0.05},
            'INDEX1_INDEX3': {'G_stat': G_13, 'p_value': p_13, 'independant': p_13 > 0.05},
            'INDEX2_INDEX3': {'G_stat': G_23, 'p_value': p_23, 'independant': p_23 > 0.05}
        }

        # Application de l'AEP : séquences typiques
        def sequences_typiques(joint_seq, epsilon=0.1):
            """Identifie les séquences epsilon-typiques"""
            counts = Counter(joint_seq)
            total = len(joint_seq)
            entropy_empirique = calculer_entropie(joint_seq)

            sequences_typ = []
            for seq, count in counts.items():
                p_empirique = count / total
                if p_empirique > 0:
                    log_prob_per_symbol = np.log2(p_empirique)
                    if abs(log_prob_per_symbol + entropy_empirique) <= epsilon:
                        sequences_typ.append((seq, count, p_empirique))

            return sequences_typ

        # Séquences typiques pour différentes dimensions
        typ_12 = sequences_typiques(joint_12)
        typ_13 = sequences_typiques(joint_13)
        typ_123 = sequences_typiques(joint_123)

        resultats['sequences_typiques'] = {
            'INDEX1_INDEX2': len(typ_12),
            'INDEX1_INDEX3': len(typ_13),
            'INDEX1_INDEX2_INDEX3': len(typ_123),
            'proportion_typiques_123': len(typ_123) / len(set(joint_123))
        }

        print(f"   ✅ Information mutuelle I(1;2) : {I_12:.4f} bits")
        print(f"   ✅ Information mutuelle I(1;3) : {I_13:.4f} bits")
        print(f"   ✅ Interaction d'ordre 3 : {I_123:.4f} bits")
        print(f"   ✅ Séquences typiques : {len(typ_123)} patterns")

        return resultats

    def _analyser_complexite_kolmogorov(self) -> Dict:
        """
        COMPLEXITÉ DE KOLMOGOROV ET UNIVERSALITÉ

        Estime la complexité de Kolmogorov des séquences INDEX5
        Teste l'aléatorité algorithmique
        Applique la compression universelle

        Returns:
            Dict: Résultats de l'analyse de complexité
        """
        print("🧮 Complexité de Kolmogorov et universalité")

        resultats = {}

        # Conversion des séquences en chaînes pour compression
        index1_str = ''.join(map(str, self.sequences['index1']))
        index5_str = ''.join(self.sequences['index5'])

        # Test de compressibilité (approximation de la complexité de Kolmogorov)
        import zlib
        import gzip

        def taux_compression(data_str):
            """Calcule le taux de compression avec différents algorithmes"""
            data_bytes = data_str.encode('utf-8')

            # Compression zlib (DEFLATE)
            compressed_zlib = zlib.compress(data_bytes)
            ratio_zlib = len(compressed_zlib) / len(data_bytes)

            # Compression gzip
            compressed_gzip = gzip.compress(data_bytes)
            ratio_gzip = len(compressed_gzip) / len(data_bytes)

            return {
                'taille_originale': len(data_bytes),
                'taille_zlib': len(compressed_zlib),
                'taille_gzip': len(compressed_gzip),
                'ratio_zlib': ratio_zlib,
                'ratio_gzip': ratio_gzip,
                'complexite_estimee': min(ratio_zlib, ratio_gzip)
            }

        # Analyse de compressibilité
        compression_index1 = taux_compression(index1_str)
        compression_index5 = taux_compression(index5_str)

        resultats['compression'] = {
            'INDEX1': compression_index1,
            'INDEX5': compression_index5
        }

        # Test d'aléatorité algorithmique par blocs
        def test_aleatoire_blocs(sequence, taille_bloc=100):
            """Test d'aléatorité par analyse de blocs"""
            if len(sequence) < taille_bloc:
                return {'erreur': 'Séquence trop courte'}

            nb_blocs = len(sequence) // taille_bloc
            complexites_blocs = []

            for i in range(nb_blocs):
                bloc = sequence[i*taille_bloc:(i+1)*taille_bloc]
                bloc_str = ''.join(map(str, bloc))
                compression = taux_compression(bloc_str)
                complexites_blocs.append(compression['complexite_estimee'])

            # Statistiques sur les complexités des blocs
            complexites_array = np.array(complexites_blocs)

            return {
                'nb_blocs': nb_blocs,
                'complexite_moyenne': np.mean(complexites_array),
                'complexite_std': np.std(complexites_array),
                'complexite_min': np.min(complexites_array),
                'complexite_max': np.max(complexites_array),
                'uniformite': 1.0 - np.std(complexites_array) / np.mean(complexites_array)
            }

        # Test par blocs sur INDEX1
        test_blocs_index1 = test_aleatoire_blocs(self.sequences['index1'])
        resultats['test_blocs'] = test_blocs_index1

        # Analyse de la complexité de Lempel-Ziv
        def complexite_lempel_ziv(sequence):
            """Calcule la complexité de Lempel-Ziv"""
            if len(sequence) == 0:
                return 0

            sequence_str = ''.join(map(str, sequence))
            n = len(sequence_str)

            # Algorithme de Lempel-Ziv 76
            i = 0
            c = 0  # Compteur de complexité

            while i < n:
                j = i + 1

                # Chercher la plus longue sous-chaîne qui apparaît avant
                while j <= n:
                    substring = sequence_str[i:j]
                    if substring not in sequence_str[:i]:
                        break
                    j += 1

                c += 1
                i = j - 1 if j > i + 1 else i + 1

            return c

        # Complexité LZ pour différentes séquences
        lz_index1 = complexite_lempel_ziv(self.sequences['index1'])
        lz_index2 = complexite_lempel_ziv(self.sequences['index2'])
        lz_index3 = complexite_lempel_ziv(self.sequences['index3'])

        # Complexité normalisée (par rapport à la longueur)
        n = len(self.sequences['index1'])
        lz_norm_index1 = lz_index1 / n if n > 0 else 0

        resultats['lempel_ziv'] = {
            'LZ_INDEX1': lz_index1,
            'LZ_INDEX2': lz_index2,
            'LZ_INDEX3': lz_index3,
            'LZ_normalized_INDEX1': lz_norm_index1,
            'longueur_sequence': n
        }

        # Test de randomness basé sur la complexité
        # Comparaison avec séquence aléatoire de même longueur
        np.random.seed(42)  # Pour reproductibilité
        sequence_aleatoire = np.random.randint(0, 2, size=len(self.sequences['index1']))
        lz_aleatoire = complexite_lempel_ziv(sequence_aleatoire)

        ratio_complexite = lz_index1 / lz_aleatoire if lz_aleatoire > 0 else 0

        resultats['comparaison_aleatoire'] = {
            'LZ_sequence_reelle': lz_index1,
            'LZ_sequence_aleatoire': lz_aleatoire,
            'ratio_complexite': ratio_complexite,
            'interpretation': 'STRUCTURE DÉTECTÉE' if ratio_complexite < 0.8 else 'PROCHE DU HASARD'
        }

        print(f"   ✅ Taux compression INDEX1 : {compression_index1['complexite_estimee']:.3f}")
        print(f"   ✅ Complexité LZ normalisée : {lz_norm_index1:.3f}")
        print(f"   ✅ Ratio vs aléatoire : {ratio_complexite:.3f}")

        return resultats

    def _analyser_harmoniques_avances(self) -> Dict:
        """
        ANALYSE HARMONIQUE ET TRANSFORMÉES DE FOURIER AVANCÉES

        Décomposition spectrale des séquences INDEX5
        Détection d'harmoniques cachées
        Analyse des fréquences caractéristiques

        Returns:
            Dict: Résultats de l'analyse harmonique avancée
        """
        print("🎵 Analyse harmonique et transformées avancées")

        resultats = {}

        # Préparation des séquences numériques
        index1_numeric = self.sequences['index1'].astype(float)

        # Conversion INDEX2 en numérique (A=0, B=1, C=2)
        index2_mapping = {'A': 0, 'B': 1, 'C': 2}
        index2_numeric = np.array([index2_mapping.get(x, 0) for x in self.sequences['index2']])

        # Conversion INDEX3 en numérique (PLAYER=0, BANKER=1, TIE=2)
        index3_mapping = {'PLAYER': 0, 'BANKER': 1, 'TIE': 2}
        index3_numeric = np.array([index3_mapping.get(x, 0) for x in self.sequences['index3']])

        # Analyse de Fourier avancée pour INDEX1
        def analyse_fourier_avancee(signal, nom_signal):
            """Analyse de Fourier complète avec détection d'harmoniques"""
            n = len(signal)

            # FFT
            fft_result = fft(signal)
            freqs = fftfreq(n)

            # Densité spectrale de puissance
            psd = np.abs(fft_result)**2 / n

            # Garder seulement les fréquences positives
            positive_mask = freqs > 0
            freqs_pos = freqs[positive_mask]
            psd_pos = psd[positive_mask]

            # Détection des pics spectraux
            # Seuil adaptatif basé sur la médiane
            seuil = np.median(psd_pos) + 2 * np.std(psd_pos)
            peaks, properties = signal.find_peaks(psd_pos, height=seuil, distance=5)

            # Analyse des harmoniques détectées
            harmoniques = []
            for peak in peaks:
                freq = freqs_pos[peak]
                amplitude = psd_pos[peak]
                periode = 1.0 / freq if freq > 0 else np.inf

                harmoniques.append({
                    'frequence': freq,
                    'periode': periode,
                    'amplitude': amplitude,
                    'amplitude_relative': amplitude / np.max(psd_pos)
                })

            # Trier par amplitude décroissante
            harmoniques.sort(key=lambda x: x['amplitude'], reverse=True)

            return {
                'frequences': freqs_pos.tolist(),
                'psd': psd_pos.tolist(),
                'harmoniques': harmoniques[:10],  # Top 10
                'nb_harmoniques': len(harmoniques),
                'energie_totale': np.sum(psd_pos),
                'frequence_dominante': freqs_pos[np.argmax(psd_pos)] if len(psd_pos) > 0 else 0
            }

        # Analyses harmoniques pour chaque INDEX
        resultats['INDEX1'] = analyse_fourier_avancee(index1_numeric, 'INDEX1')
        resultats['INDEX2'] = analyse_fourier_avancee(index2_numeric, 'INDEX2')
        resultats['INDEX3'] = analyse_fourier_avancee(index3_numeric, 'INDEX3')

        # Analyse de cohérence spectrale entre INDEX
        def coherence_spectrale(signal1, signal2):
            """Calcule la cohérence spectrale entre deux signaux"""
            from scipy.signal import coherence

            try:
                freqs, coh = coherence(signal1, signal2, nperseg=min(256, len(signal1)//4))

                # Cohérence moyenne et maximale
                coherence_moyenne = np.mean(coh)
                coherence_max = np.max(coh)
                freq_max_coh = freqs[np.argmax(coh)]

                return {
                    'frequences': freqs.tolist(),
                    'coherence': coh.tolist(),
                    'coherence_moyenne': coherence_moyenne,
                    'coherence_max': coherence_max,
                    'freq_coherence_max': freq_max_coh
                }
            except Exception as e:
                return {'erreur': str(e)}

        # Cohérences entre les différents INDEX
        resultats['coherence_INDEX1_INDEX2'] = coherence_spectrale(index1_numeric, index2_numeric)
        resultats['coherence_INDEX1_INDEX3'] = coherence_spectrale(index1_numeric, index3_numeric)
        resultats['coherence_INDEX2_INDEX3'] = coherence_spectrale(index2_numeric, index3_numeric)

        # Analyse des oscillations de basse fréquence
        def detecter_oscillations_lentes(signal, seuil_periode=50):
            """Détecte les oscillations de période > seuil_periode"""
            fft_result = fft(signal)
            freqs = fftfreq(len(signal))
            psd = np.abs(fft_result)**2

            # Fréquences correspondant aux périodes lentes
            freq_lentes = freqs[(freqs > 0) & (freqs < 1.0/seuil_periode)]
            psd_lentes = psd[(freqs > 0) & (freqs < 1.0/seuil_periode)]

            if len(psd_lentes) > 0:
                energie_lente = np.sum(psd_lentes)
                energie_totale = np.sum(psd[freqs > 0])
                proportion_lente = energie_lente / energie_totale if energie_totale > 0 else 0

                return {
                    'energie_oscillations_lentes': energie_lente,
                    'proportion_energie_lente': proportion_lente,
                    'nb_frequences_lentes': len(freq_lentes)
                }
            else:
                return {'energie_oscillations_lentes': 0, 'proportion_energie_lente': 0}

        # Détection d'oscillations lentes pour INDEX1
        oscillations_lentes = detecter_oscillations_lentes(index1_numeric)
        resultats['oscillations_lentes'] = oscillations_lentes

        print(f"   ✅ Harmoniques INDEX1 : {resultats['INDEX1']['nb_harmoniques']}")
        print(f"   ✅ Fréquence dominante : {resultats['INDEX1']['frequence_dominante']:.4f}")
        print(f"   ✅ Oscillations lentes : {oscillations_lentes['proportion_energie_lente']:.3f}")

        return resultats

    def _appliquer_transformations_series(self) -> Dict:
        """
        TRANSFORMATIONS DE SÉRIES (Kummer, Euler)

        Applique les transformations de Kummer et Euler
        Accélère la convergence des calculs statistiques
        Améliore la précision numérique

        Returns:
            Dict: Résultats des transformations de séries
        """
        print("🔄 Transformations de séries (Kummer, Euler)")

        resultats = {}

        # Calcul des autocorrélations avec transformation d'Euler
        def autocorrelation_avec_euler(sequence, max_lag=20):
            """Calcule les autocorrélations avec transformation d'Euler pour accélération"""
            sequence = np.array(sequence, dtype=float)
            n = len(sequence)

            # Centrer la séquence
            sequence_centree = sequence - np.mean(sequence)

            autocorrs = []
            autocorrs_euler = []

            for lag in range(max_lag + 1):
                if lag == 0:
                    autocorr = 1.0
                elif lag < n:
                    # Autocorrélation standard
                    c_lag = np.mean(sequence_centree[:-lag] * sequence_centree[lag:])
                    c_0 = np.var(sequence_centree)
                    autocorr = c_lag / c_0 if c_0 > 0 else 0
                else:
                    autocorr = 0

                autocorrs.append(autocorr)

                # Transformation d'Euler pour accélération de convergence
                # S_n = sum_{k=0}^n (-1)^k * a_k
                # Euler: S = (S_n + S_{n+1}) / 2
                if lag > 0:
                    euler_transform = (autocorr + autocorrs[-2]) / 2 if len(autocorrs) > 1 else autocorr
                else:
                    euler_transform = autocorr

                autocorrs_euler.append(euler_transform)

            return autocorrs, autocorrs_euler

        # Application aux séquences INDEX
        autocorr_index1, autocorr_euler_index1 = autocorrelation_avec_euler(self.sequences['index1'])

        resultats['autocorrelations'] = {
            'INDEX1_standard': autocorr_index1,
            'INDEX1_euler': autocorr_euler_index1,
            'amelioration_convergence': np.mean(np.abs(np.diff(autocorr_euler_index1))) < np.mean(np.abs(np.diff(autocorr_index1)))
        }

        # Transformation de Kummer pour séries de corrélations croisées
        def correlation_croisee_kummer(seq1, seq2, max_lag=10):
            """Corrélations croisées avec transformation de Kummer"""
            seq1 = np.array(seq1, dtype=float)
            seq2 = np.array(seq2, dtype=float)

            # Centrer les séquences
            seq1_centree = seq1 - np.mean(seq1)
            seq2_centree = seq2 - np.mean(seq2)

            correlations = []
            correlations_kummer = []

            for lag in range(-max_lag, max_lag + 1):
                if lag == 0:
                    corr = np.corrcoef(seq1_centree, seq2_centree)[0, 1]
                elif lag > 0 and lag < len(seq1):
                    corr = np.corrcoef(seq1_centree[:-lag], seq2_centree[lag:])[0, 1]
                elif lag < 0 and abs(lag) < len(seq2):
                    corr = np.corrcoef(seq1_centree[abs(lag):], seq2_centree[:lag])[0, 1]
                else:
                    corr = 0

                if np.isnan(corr):
                    corr = 0

                correlations.append(corr)

                # Transformation de Kummer : utilise la relation de récurrence
                # pour accélérer la convergence
                if len(correlations) > 2:
                    # Approximation de Kummer basée sur les termes précédents
                    kummer_transform = correlations[-1] + (correlations[-1] - correlations[-2])**2 / (correlations[-2] - correlations[-3]) if correlations[-2] != correlations[-3] else correlations[-1]
                else:
                    kummer_transform = corr

                correlations_kummer.append(kummer_transform)

            return correlations, correlations_kummer

        # Corrélations croisées entre INDEX avec transformation de Kummer
        # Conversion des INDEX2 et INDEX3 en numérique
        index2_num = np.array([{'A': 0, 'B': 1, 'C': 2}.get(x, 0) for x in self.sequences['index2']])
        index3_num = np.array([{'PLAYER': 0, 'BANKER': 1, 'TIE': 2}.get(x, 0) for x in self.sequences['index3']])

        corr_12, corr_12_kummer = correlation_croisee_kummer(self.sequences['index1'], index2_num)
        corr_13, corr_13_kummer = correlation_croisee_kummer(self.sequences['index1'], index3_num)

        resultats['correlations_croisees'] = {
            'INDEX1_INDEX2_standard': corr_12,
            'INDEX1_INDEX2_kummer': corr_12_kummer,
            'INDEX1_INDEX3_standard': corr_13,
            'INDEX1_INDEX3_kummer': corr_13_kummer
        }

        # Calcul de séries de puissances avec transformations
        def serie_puissance_transformee(sequence, ordre_max=5):
            """Calcule les moments avec transformations pour stabilité numérique"""
            sequence = np.array(sequence, dtype=float)

            moments_standard = []
            moments_transformes = []

            for k in range(1, ordre_max + 1):
                # Moment standard
                moment = np.mean(sequence**k)
                moments_standard.append(moment)

                # Transformation pour stabilité (utilise la méthode des cumulants)
                if k == 1:
                    moment_transforme = moment
                elif k == 2:
                    moment_transforme = moment - moments_standard[0]**2
                elif k == 3:
                    moment_transforme = moment - 3*moments_standard[1]*moments_standard[0] + 2*moments_standard[0]**3
                else:
                    # Approximation pour ordres supérieurs
                    moment_transforme = moment

                moments_transformes.append(moment_transforme)

            return moments_standard, moments_transformes

        # Application aux séquences
        moments_std, moments_trans = serie_puissance_transformee(self.sequences['index1'])

        resultats['moments'] = {
            'moments_standard': moments_std,
            'moments_transformes': moments_trans,
            'stabilite_numerique': np.all(np.isfinite(moments_trans))
        }

        print(f"   ✅ Autocorrélations Euler calculées")
        print(f"   ✅ Corrélations croisées Kummer appliquées")
        print(f"   ✅ Stabilité numérique : {resultats['moments']['stabilite_numerique']}")

        return resultats

    def _analyser_information_fisher(self) -> Dict:
        """
        INFORMATION DE FISHER ET EFFICACITÉ STATISTIQUE

        Calcule l'information de Fisher pour les paramètres SYNC/DESYNC
        Applique les bornes de Cramér-Rao
        Mesure l'efficacité statistique optimale

        Returns:
            Dict: Résultats de l'analyse de Fisher
        """
        print("📊 Information de Fisher et efficacité statistique")

        resultats = {}

        # Estimation du paramètre p (proportion SYNC)
        sync_count = np.sum(self.sequences['index1'] == 0)
        n = len(self.sequences['index1'])
        p_hat = sync_count / n

        # Information de Fisher pour distribution binomiale
        # I(p) = n / (p * (1-p))
        if p_hat > 0 and p_hat < 1:
            fisher_info = n / (p_hat * (1 - p_hat))
        else:
            fisher_info = np.inf

        # Borne de Cramér-Rao (variance minimale)
        cramer_rao_bound = 1 / fisher_info if fisher_info > 0 and fisher_info != np.inf else np.inf

        # Variance empirique de l'estimateur
        variance_empirique = p_hat * (1 - p_hat) / n

        # Efficacité de l'estimateur
        efficacite = cramer_rao_bound / variance_empirique if variance_empirique > 0 else 0

        resultats['fisher_binomial'] = {
            'p_estime': p_hat,
            'information_fisher': fisher_info,
            'borne_cramer_rao': cramer_rao_bound,
            'variance_empirique': variance_empirique,
            'efficacite': efficacite,
            'estimateur_optimal': abs(efficacite - 1.0) < 0.01
        }

        # Information de Fisher pour modèle de Markov
        # Calcul basé sur la matrice de transition
        index1_seq = self.sequences['index1']

        # Transitions observées
        n_00 = np.sum((index1_seq[:-1] == 0) & (index1_seq[1:] == 0))
        n_01 = np.sum((index1_seq[:-1] == 0) & (index1_seq[1:] == 1))
        n_10 = np.sum((index1_seq[:-1] == 1) & (index1_seq[1:] == 0))
        n_11 = np.sum((index1_seq[:-1] == 1) & (index1_seq[1:] == 1))

        # Probabilités de transition estimées
        if n_00 + n_01 > 0:
            p_00 = n_00 / (n_00 + n_01)
            p_01 = n_01 / (n_00 + n_01)
        else:
            p_00 = p_01 = 0

        if n_10 + n_11 > 0:
            p_10 = n_10 / (n_10 + n_11)
            p_11 = n_11 / (n_10 + n_11)
        else:
            p_10 = p_11 = 0

        # Information de Fisher pour les paramètres de transition
        # Matrice d'information de Fisher pour modèle multinomial
        fisher_markov = {}

        if p_00 > 0 and p_01 > 0:
            fisher_00 = (n_00 + n_01) / (p_00 * p_01)
            fisher_markov['transition_0'] = fisher_00

        if p_10 > 0 and p_11 > 0:
            fisher_11 = (n_10 + n_11) / (p_10 * p_11)
            fisher_markov['transition_1'] = fisher_11

        resultats['fisher_markov'] = {
            'transitions': {
                'n_00': n_00, 'n_01': n_01, 'n_10': n_10, 'n_11': n_11,
                'p_00': p_00, 'p_01': p_01, 'p_10': p_10, 'p_11': p_11
            },
            'information_fisher': fisher_markov
        }

        # Test de l'hypothèse d'indépendance vs Markov
        # Likelihood ratio test
        # Log-vraisemblance sous H0 (indépendance)
        if p_hat > 0 and p_hat < 1:
            log_lik_h0 = sync_count * np.log(p_hat) + (n - sync_count) * np.log(1 - p_hat)
        else:
            log_lik_h0 = -np.inf

        # Log-vraisemblance sous H1 (Markov)
        log_lik_h1 = 0
        if p_00 > 0:
            log_lik_h1 += n_00 * np.log(p_00)
        if p_01 > 0:
            log_lik_h1 += n_01 * np.log(p_01)
        if p_10 > 0:
            log_lik_h1 += n_10 * np.log(p_10)
        if p_11 > 0:
            log_lik_h1 += n_11 * np.log(p_11)

        # Statistique du test
        likelihood_ratio = 2 * (log_lik_h1 - log_lik_h0)
        p_value_lr = 1 - stats.chi2.cdf(likelihood_ratio, df=1) if likelihood_ratio > 0 else 1.0

        resultats['test_likelihood_ratio'] = {
            'log_lik_independance': log_lik_h0,
            'log_lik_markov': log_lik_h1,
            'statistique_lr': likelihood_ratio,
            'p_value': p_value_lr,
            'markov_prefere': p_value_lr < 0.05
        }

        print(f"   ✅ Information Fisher : {fisher_info:.2f}")
        print(f"   ✅ Efficacité estimateur : {efficacite:.3f}")
        print(f"   ✅ Test Markov vs indép. : p = {p_value_lr:.4f}")

        return resultats

    def _utiliser_fonctions_speciales(self) -> Dict:
        """
        FONCTIONS SPÉCIALES POUR MODÉLISATION PRÉCISE

        Utilise les fonctions Gamma, Beta, Error pour modéliser
        les distributions baccarat Lupasco
        Calcule des probabilités précises

        Returns:
            Dict: Résultats avec fonctions spéciales
        """
        print("🎯 Fonctions spéciales pour modélisation précise")

        resultats = {}

        # Modélisation des distributions avec fonction Beta
        def ajuster_distribution_beta(sequence):
            """Ajuste une distribution Beta aux données"""
            sequence = np.array(sequence, dtype=float)

            # Normaliser entre 0 et 1
            seq_norm = (sequence - np.min(sequence)) / (np.max(sequence) - np.min(sequence))
            seq_norm = seq_norm[seq_norm > 0]  # Éviter les valeurs exactement 0 ou 1
            seq_norm = seq_norm[seq_norm < 1]

            if len(seq_norm) < 2:
                return {'erreur': 'Données insuffisantes'}

            # Estimation des paramètres par méthode des moments
            mean_emp = np.mean(seq_norm)
            var_emp = np.var(seq_norm)

            if var_emp > 0 and mean_emp > 0 and mean_emp < 1:
                # Paramètres Beta
                alpha = mean_emp * (mean_emp * (1 - mean_emp) / var_emp - 1)
                beta_param = (1 - mean_emp) * (mean_emp * (1 - mean_emp) / var_emp - 1)

                if alpha > 0 and beta_param > 0:
                    # Test de Kolmogorov-Smirnov
                    from scipy.stats import beta as beta_dist
                    ks_stat, p_value = stats.kstest(seq_norm,
                                                   lambda x: beta_dist.cdf(x, alpha, beta_param))

                    return {
                        'alpha': alpha,
                        'beta': beta_param,
                        'ks_statistic': ks_stat,
                        'p_value': p_value,
                        'ajustement_valide': p_value > 0.05
                    }

            return {'erreur': 'Paramètres invalides'}

        # Application aux séquences numériques
        index1_float = self.sequences['index1'].astype(float)
        cards_count_float = self.sequences['cards_count'].astype(float)

        beta_index1 = ajuster_distribution_beta(index1_float)
        beta_cards = ajuster_distribution_beta(cards_count_float)

        resultats['distributions_beta'] = {
            'INDEX1': beta_index1,
            'cards_count': beta_cards
        }

        # Utilisation de la fonction Gamma pour modéliser les intervalles
        def analyser_intervalles_gamma(sequence):
            """Analyse les intervalles entre événements avec distribution Gamma"""
            # Calculer les intervalles entre changements d'état
            changes = np.where(np.diff(sequence) != 0)[0]
            if len(changes) < 2:
                return {'erreur': 'Pas assez de changements'}

            intervalles = np.diff(changes)
            if len(intervalles) < 2:
                return {'erreur': 'Pas assez d\'intervalles'}

            # Ajustement Gamma
            try:
                # Estimation des paramètres
                shape, loc, scale = stats.gamma.fit(intervalles, floc=0)

                # Test de Kolmogorov-Smirnov
                ks_stat, p_value = stats.kstest(intervalles,
                                               lambda x: stats.gamma.cdf(x, shape, loc, scale))

                # Calcul de la fonction Gamma
                gamma_value = gamma(shape)

                return {
                    'shape': shape,
                    'scale': scale,
                    'gamma_function': gamma_value,
                    'ks_statistic': ks_stat,
                    'p_value': p_value,
                    'ajustement_valide': p_value > 0.05,
                    'nb_intervalles': len(intervalles)
                }
            except Exception as e:
                return {'erreur': str(e)}

        # Analyse des intervalles pour INDEX1
        gamma_intervalles = analyser_intervalles_gamma(self.sequences['index1'])
        resultats['gamma_intervalles'] = gamma_intervalles

        # Utilisation de la fonction Error (erf) pour probabilités cumulatives
        def probabilites_error_function(sequence):
            """Calcule des probabilités avec fonction d'erreur"""
            sequence = np.array(sequence, dtype=float)

            # Standardisation
            mean_seq = np.mean(sequence)
            std_seq = np.std(sequence)

            if std_seq > 0:
                z_scores = (sequence - mean_seq) / std_seq

                # Probabilités cumulatives avec fonction d'erreur
                # P(X <= x) = 0.5 * (1 + erf(z/sqrt(2)))
                prob_cumul = 0.5 * (1 + erf(z_scores / np.sqrt(2)))

                # Fonction d'erreur complémentaire
                prob_compl = erfc(z_scores / np.sqrt(2))

                return {
                    'z_scores': z_scores.tolist(),
                    'probabilites_cumulatives': prob_cumul.tolist(),
                    'probabilites_complementaires': prob_compl.tolist(),
                    'moyenne': mean_seq,
                    'ecart_type': std_seq
                }
            else:
                return {'erreur': 'Écart-type nul'}

        # Application aux séquences
        erf_index1 = probabilites_error_function(index1_float)
        erf_cards = probabilites_error_function(cards_count_float)

        resultats['error_functions'] = {
            'INDEX1': erf_index1,
            'cards_count': erf_cards
        }

        # Tests exacts basés sur les fonctions spéciales
        def test_exact_hypergeometrique(sequence):
            """Test exact basé sur la distribution hypergéométrique"""
            # Pour tester l'équilibre SYNC/DESYNC
            sync_count = np.sum(sequence == 0)
            total = len(sequence)

            # Test hypergéométrique exact
            # H0: proportion = 0.5
            p_value_exact = stats.binom_test(sync_count, total, 0.5)

            # Utilisation de la fonction Beta pour le calcul exact
            # P-value = I_{0.5}(sync_count + 1, total - sync_count + 1)
            # où I est la fonction Beta incomplète régularisée
            from scipy.special import betainc

            if sync_count < total // 2:
                p_beta = 2 * betainc(sync_count + 1, total - sync_count + 1, 0.5)
            else:
                p_beta = 2 * betainc(total - sync_count + 1, sync_count + 1, 0.5)

            return {
                'sync_count': sync_count,
                'total': total,
                'p_value_binomial': p_value_exact,
                'p_value_beta': p_beta,
                'coherence': abs(p_value_exact - p_beta) < 1e-10
            }

        test_exact = test_exact_hypergeometrique(self.sequences['index1'])
        resultats['test_exact'] = test_exact

        print(f"   ✅ Distributions Beta ajustées")
        print(f"   ✅ Intervalles Gamma analysés")
        print(f"   ✅ Test exact : p = {test_exact['p_value_beta']:.6f}")

        return resultats

    def _estimation_entropie_maximale_generalisee(self) -> Dict:
        """
        ESTIMATION PAR ENTROPIE MAXIMALE GÉNÉRALISÉE

        Reconstruction de distributions à partir de moments
        Estimation de densités spectrales par entropie maximale
        Prédiction optimale sous contraintes

        Returns:
            Dict: Résultats de l'estimation par entropie maximale
        """
        print("🎯 Estimation par entropie maximale généralisée")

        resultats = {}

        # Estimation de distribution par entropie maximale avec contraintes de moments
        def estimation_maxent_moments(sequence, nb_moments=4):
            """Estime la distribution par entropie maximale avec contraintes de moments"""
            sequence = np.array(sequence, dtype=float)

            # Calcul des moments empiriques
            moments_empiriques = []
            for k in range(1, nb_moments + 1):
                moment = np.mean(sequence**k)
                moments_empiriques.append(moment)

            # Optimisation pour trouver les multiplicateurs de Lagrange
            def objectif(lambdas):
                """Fonction objectif pour l'optimisation"""
                # Distribution exponentielle avec contraintes
                x_test = np.linspace(np.min(sequence), np.max(sequence), 100)

                # Calcul de la fonction de partition
                exp_terms = np.zeros_like(x_test)
                for i, lam in enumerate(lambdas):
                    exp_terms += lam * (x_test**(i+1))

                # Éviter l'overflow
                exp_terms = np.clip(exp_terms, -500, 500)
                Z = np.trapz(np.exp(-exp_terms), x_test)

                if Z <= 0:
                    return np.inf

                # Moments théoriques
                moments_theoriques = []
                for k in range(1, nb_moments + 1):
                    integrand = (x_test**k) * np.exp(-exp_terms) / Z
                    moment_theo = np.trapz(integrand, x_test)
                    moments_theoriques.append(moment_theo)

                # Erreur quadratique
                erreur = np.sum([(m_emp - m_theo)**2
                               for m_emp, m_theo in zip(moments_empiriques, moments_theoriques)])
                return erreur

            # Optimisation
            try:
                from scipy.optimize import minimize
                result = minimize(objectif, np.zeros(nb_moments), method='BFGS')

                if result.success:
                    lambdas_opt = result.x

                    # Calcul de l'entropie de la distribution estimée
                    x_test = np.linspace(np.min(sequence), np.max(sequence), 100)
                    exp_terms = np.zeros_like(x_test)
                    for i, lam in enumerate(lambdas_opt):
                        exp_terms += lam * (x_test**(i+1))

                    exp_terms = np.clip(exp_terms, -500, 500)
                    Z = np.trapz(np.exp(-exp_terms), x_test)

                    if Z > 0:
                        pdf = np.exp(-exp_terms) / Z
                        entropie = -np.trapz(pdf * np.log(pdf + 1e-10), x_test)

                        return {
                            'lambdas': lambdas_opt.tolist(),
                            'moments_empiriques': moments_empiriques,
                            'entropie_maximale': entropie,
                            'convergence': True,
                            'x_support': x_test.tolist(),
                            'pdf_estimee': pdf.tolist()
                        }

                return {'convergence': False, 'erreur': 'Optimisation échouée'}

            except Exception as e:
                return {'convergence': False, 'erreur': str(e)}

        # Application aux séquences
        maxent_index1 = estimation_maxent_moments(self.sequences['index1'].astype(float))
        maxent_cards = estimation_maxent_moments(self.sequences['cards_count'].astype(float))

        resultats['maxent_distributions'] = {
            'INDEX1': maxent_index1,
            'cards_count': maxent_cards
        }

        # Prédiction optimale sous contraintes
        def prediction_optimale(sequence, horizon=5):
            """Prédiction optimale par entropie maximale"""
            sequence = np.array(sequence)

            # Modèle autorégressif avec entropie maximale
            # Utilise les autocorrélations comme contraintes

            # Calcul des autocorrélations
            autocorrs = []
            for lag in range(1, min(horizon + 1, len(sequence) // 4)):
                if lag < len(sequence):
                    autocorr = np.corrcoef(sequence[:-lag], sequence[lag:])[0, 1]
                    if not np.isnan(autocorr):
                        autocorrs.append(autocorr)
                    else:
                        autocorrs.append(0)
                else:
                    autocorrs.append(0)

            # Prédiction basée sur le modèle AR avec contraintes d'entropie
            if len(autocorrs) > 0:
                # Coefficients AR estimés par Yule-Walker
                try:
                    from scipy.linalg import solve_toeplitz

                    if len(autocorrs) > 1:
                        # Matrice de Toeplitz des autocorrélations
                        R = toeplitz(autocorrs[:-1])
                        r = np.array(autocorrs[1:])

                        # Résolution du système de Yule-Walker
                        coeffs_ar = solve_toeplitz(R, r)

                        # Prédiction
                        predictions = []
                        seq_extended = sequence.copy()

                        for h in range(horizon):
                            if len(seq_extended) >= len(coeffs_ar):
                                pred = np.dot(coeffs_ar, seq_extended[-len(coeffs_ar):])
                                predictions.append(pred)
                                seq_extended = np.append(seq_extended, pred)
                            else:
                                predictions.append(np.mean(sequence))

                        return {
                            'coefficients_ar': coeffs_ar.tolist(),
                            'autocorrelations': autocorrs,
                            'predictions': predictions,
                            'horizon': horizon,
                            'methode': 'AR_maxent'
                        }

                except Exception as e:
                    return {'erreur': str(e)}

            # Fallback: prédiction par moyenne
            return {
                'predictions': [np.mean(sequence)] * horizon,
                'horizon': horizon,
                'methode': 'moyenne'
            }

        # Prédictions pour INDEX1
        pred_index1 = prediction_optimale(self.sequences['index1'].astype(float))
        resultats['predictions'] = {
            'INDEX1': pred_index1
        }

        # Estimation de densité spectrale par entropie maximale
        def densite_spectrale_maxent(sequence, ordre=10):
            """Estime la densité spectrale par entropie maximale"""
            sequence = np.array(sequence, dtype=float)

            # Calcul des autocorrélations
            autocorrs = [1.0]  # R(0) = 1
            for lag in range(1, ordre + 1):
                if lag < len(sequence):
                    seq_centered = sequence - np.mean(sequence)
                    autocorr = np.mean(seq_centered[:-lag] * seq_centered[lag:]) / np.var(seq_centered)
                    autocorrs.append(autocorr)
                else:
                    autocorrs.append(0)

            # Méthode de Burg pour estimation par entropie maximale
            # (déjà implémentée dans _estimation_spectrale_entropie_maximale)

            # Densité spectrale par transformée de Fourier des autocorrélations
            autocorrs_padded = np.pad(autocorrs, (0, 1024 - len(autocorrs)), 'constant')
            psd_maxent = np.abs(fft(autocorrs_padded))**2
            freqs = fftfreq(len(autocorrs_padded))

            # Garder seulement les fréquences positives
            positive_mask = freqs >= 0
            freqs_pos = freqs[positive_mask]
            psd_pos = psd_maxent[positive_mask]

            return {
                'frequences': freqs_pos.tolist(),
                'densite_spectrale': psd_pos.tolist(),
                'autocorrelations': autocorrs,
                'ordre': ordre
            }

        # Densité spectrale pour INDEX1
        psd_maxent = densite_spectrale_maxent(self.sequences['index1'].astype(float))
        resultats['densite_spectrale'] = psd_maxent

        print(f"   ✅ Distributions MaxEnt estimées")
        print(f"   ✅ Prédictions calculées (horizon {pred_index1['horizon']})")
        print(f"   ✅ Densité spectrale MaxEnt générée")

        return resultats

    def _synthese_revolutionnaire(self, resultats: Dict) -> Dict:
        """
        SYNTHÈSE RÉVOLUTIONNAIRE DE L'ANALYSE COMPLÈTE

        Combine tous les résultats pour détecter le système complexe
        Quantifie les 5 phénomènes cibles
        Génère le verdict final

        Args:
            resultats: Dictionnaire complet des résultats d'analyse

        Returns:
            Dict: Synthèse révolutionnaire finale
        """
        print("🎯 SYNTHÈSE RÉVOLUTIONNAIRE FINALE")

        synthese = {
            'phenomenes_detectes': {},
            'quantifications': {},
            'preuves_convergentes': {},
            'verdict_final': {},
            'recommandations': []
        }

        # 1. MÉMOIRE SYSTÉMIQUE ET AUTO-RÉGULATION
        memoire_detectee = False
        preuves_memoire = []

        # Preuve 1: Tests d'hypothèses optimaux
        if 'tests_optimaux' in resultats:
            equilibre = resultats['tests_optimaux']['equilibre_sync_desync']
            if equilibre['p_value_exact'] < 1e-8:
                memoire_detectee = True
                preuves_memoire.append(f"Équilibre SYNC/DESYNC impossible (p={equilibre['p_value_exact']:.2e})")

        # Preuve 2: Modèles de Markov
        if 'markov_caches' in resultats:
            markov = resultats['markov_caches']['markov_simple']
            if markov['memoire_detectee']:
                memoire_detectee = True
                preuves_memoire.append(f"Mémoire markovienne détectée (p={markov['p_value_independance']:.4f})")

        # Preuve 3: Information de Fisher
        if 'information_fisher' in resultats:
            lr_test = resultats['information_fisher']['test_likelihood_ratio']
            if lr_test['markov_prefere']:
                memoire_detectee = True
                preuves_memoire.append(f"Modèle Markov préféré (LR={lr_test['statistique_lr']:.2f})")

        synthese['phenomenes_detectes']['memoire_systemique'] = {
            'detecte': memoire_detectee,
            'preuves': preuves_memoire,
            'niveau_confiance': 'TRÈS ÉLEVÉ' if len(preuves_memoire) >= 2 else 'MODÉRÉ'
        }

        # 2. CORRÉLATIONS SÉQUENTIELLES
        correlations_detectees = False
        preuves_correlations = []

        # Preuve 1: Information mutuelle
        if 'sequences_typiques' in resultats:
            info_mut = resultats['sequences_typiques']['informations_mutuelles']
            if info_mut['I_INDEX1_INDEX2'] > 0.01 or info_mut['I_INDEX1_INDEX3'] > 0.01:
                correlations_detectees = True
                preuves_correlations.append(f"Information mutuelle I(1;2)={info_mut['I_INDEX1_INDEX2']:.4f}")

        # Preuve 2: Analyse harmonique
        if 'analyse_harmonique' in resultats:
            harmoniques = resultats['analyse_harmonique']['INDEX1']['nb_harmoniques']
            if harmoniques > 0:
                correlations_detectees = True
                preuves_correlations.append(f"{harmoniques} harmoniques détectées")

        # Preuve 3: Transformations de séries
        if 'transformations_series' in resultats:
            autocorr = resultats['transformations_series']['autocorrelations']
            if autocorr['amelioration_convergence']:
                correlations_detectees = True
                preuves_correlations.append("Autocorrélations significatives (Euler)")

        synthese['phenomenes_detectes']['correlations_sequentielles'] = {
            'detecte': correlations_detectees,
            'preuves': preuves_correlations,
            'niveau_confiance': 'ÉLEVÉ' if len(preuves_correlations) >= 2 else 'MODÉRÉ'
        }

        # 3. MÉCANISMES D'ÉQUILIBRAGE/HOMÉOSTASIE
        equilibrage_detecte = False
        preuves_equilibrage = []

        # Preuve 1: Constance universelle
        if 'tests_optimaux' in resultats:
            constance = resultats['tests_optimaux']['constance_universelle']
            deviations = [cat['deviation_from_global'] for cat in constance.values()]
            if np.mean(deviations) < 0.05:  # Très faible déviation
                equilibrage_detecte = True
                preuves_equilibrage.append(f"Constance universelle (dév. moy. = {np.mean(deviations):.4f})")

        # Preuve 2: Oscillations contrôlées
        if 'analyse_harmonique' in resultats:
            oscillations = resultats['analyse_harmonique']['oscillations_lentes']
            if oscillations['proportion_energie_lente'] > 0.1:
                equilibrage_detecte = True
                preuves_equilibrage.append(f"Oscillations lentes contrôlées ({oscillations['proportion_energie_lente']:.3f})")

        synthese['phenomenes_detectes']['mecanismes_equilibrage'] = {
            'detecte': equilibrage_detecte,
            'preuves': preuves_equilibrage,
            'niveau_confiance': 'ÉLEVÉ' if len(preuves_equilibrage) >= 1 else 'FAIBLE'
        }

        # 4. PRÉDICTIBILITÉ PARTIELLE
        predictibilite_detectee = False
        preuves_predictibilite = []

        # Preuve 1: Complexité de Kolmogorov
        if 'complexite_kolmogorov' in resultats:
            complexite = resultats['complexite_kolmogorov']['comparaison_aleatoire']
            if complexite['ratio_complexite'] < 0.8:
                predictibilite_detectee = True
                preuves_predictibilite.append(f"Structure détectée (ratio={complexite['ratio_complexite']:.3f})")

        # Preuve 2: Prédictions MaxEnt
        if 'entropie_maximale' in resultats:
            predictions = resultats['entropie_maximale']['predictions']['INDEX1']
            if predictions['methode'] == 'AR_maxent':
                predictibilite_detectee = True
                preuves_predictibilite.append("Modèle AR prédictif validé")

        synthese['phenomenes_detectes']['predictibilite_partielle'] = {
            'detecte': predictibilite_detectee,
            'preuves': preuves_predictibilite,
            'niveau_confiance': 'MODÉRÉ' if len(preuves_predictibilite) >= 1 else 'FAIBLE'
        }

        # 5. SYSTÈME COMPLEXE ADAPTATIF
        systeme_complexe_detecte = False
        preuves_complexe = []

        # Critère: Au moins 3 des 4 phénomènes précédents détectés
        phenomenes_detectes_count = sum([
            synthese['phenomenes_detectes']['memoire_systemique']['detecte'],
            synthese['phenomenes_detectes']['correlations_sequentielles']['detecte'],
            synthese['phenomenes_detectes']['mecanismes_equilibrage']['detecte'],
            synthese['phenomenes_detectes']['predictibilite_partielle']['detecte']
        ])

        if phenomenes_detectes_count >= 3:
            systeme_complexe_detecte = True
            preuves_complexe.append(f"{phenomenes_detectes_count}/4 phénomènes détectés")

        # Preuve supplémentaire: Interaction d'ordre 3
        if 'sequences_typiques' in resultats:
            interaction = resultats['sequences_typiques']['informations_mutuelles']['I_interaction_ordre3']
            if abs(interaction) > 0.001:
                systeme_complexe_detecte = True
                preuves_complexe.append(f"Interaction d'ordre 3 = {interaction:.4f}")

        synthese['phenomenes_detectes']['systeme_complexe_adaptatif'] = {
            'detecte': systeme_complexe_detecte,
            'preuves': preuves_complexe,
            'niveau_confiance': 'TRÈS ÉLEVÉ' if phenomenes_detectes_count >= 3 else 'FAIBLE'
        }

        # QUANTIFICATIONS FINALES
        synthese['quantifications'] = {
            'equilibre_sync_desync': {
                'p_value': resultats.get('tests_optimaux', {}).get('equilibre_sync_desync', {}).get('p_value_exact', 1.0),
                'impossible_hasard_pur': resultats.get('tests_optimaux', {}).get('equilibre_sync_desync', {}).get('p_value_exact', 1.0) < 1e-8
            },
            'information_mutuelle_totale': {
                'I_123': resultats.get('sequences_typiques', {}).get('informations_mutuelles', {}).get('I_interaction_ordre3', 0),
                'significative': abs(resultats.get('sequences_typiques', {}).get('informations_mutuelles', {}).get('I_interaction_ordre3', 0)) > 0.001
            },
            'complexite_algorithmique': {
                'ratio_vs_aleatoire': resultats.get('complexite_kolmogorov', {}).get('comparaison_aleatoire', {}).get('ratio_complexite', 1.0),
                'structure_detectee': resultats.get('complexite_kolmogorov', {}).get('comparaison_aleatoire', {}).get('ratio_complexite', 1.0) < 0.8
            }
        }

        # VERDICT FINAL
        score_detection = phenomenes_detectes_count / 4.0

        if score_detection >= 0.75:
            verdict = "SYSTÈME COMPLEXE ORGANISÉ DÉTECTÉ"
            niveau = "RÉVOLUTIONNAIRE"
        elif score_detection >= 0.5:
            verdict = "ORGANISATION PARTIELLE DÉTECTÉE"
            niveau = "SIGNIFICATIF"
        else:
            verdict = "HASARD APPARENT AVEC STRUCTURES MINEURES"
            niveau = "LIMITÉ"

        synthese['verdict_final'] = {
            'verdict': verdict,
            'niveau_detection': niveau,
            'score_detection': score_detection,
            'phenomenes_detectes': phenomenes_detectes_count,
            'confiance_globale': 'TRÈS ÉLEVÉE' if score_detection >= 0.75 else 'MODÉRÉE'
        }

        # RECOMMANDATIONS
        if systeme_complexe_detecte:
            synthese['recommandations'] = [
                "Approfondir l'analyse avec plus de données",
                "Développer des modèles prédictifs basés sur les patterns détectés",
                "Étudier les mécanismes d'auto-régulation identifiés",
                "Publier les résultats dans une revue scientifique spécialisée"
            ]
        else:
            synthese['recommandations'] = [
                "Augmenter la taille de l'échantillon",
                "Affiner les méthodes de détection",
                "Rechercher d'autres types de patterns"
            ]

        print(f"   🎯 VERDICT : {verdict}")
        print(f"   📊 Score de détection : {score_detection:.2f}")
        print(f"   🔬 Phénomènes détectés : {phenomenes_detectes_count}/4")

        return synthese

    def generer_rapport_complet(self, fichier_sortie: str = None) -> str:
        """
        Génère un rapport complet de l'analyse révolutionnaire

        Args:
            fichier_sortie: Nom du fichier de sortie (optionnel)

        Returns:
            str: Contenu du rapport
        """
        if not self.resultats:
            return "❌ Aucune analyse effectuée. Lancez d'abord analyser_systeme_complexe_complet()"

        rapport = []
        rapport.append("=" * 100)
        rapport.append("RAPPORT D'ANALYSE RÉVOLUTIONNAIRE BACCARAT LUPASCO")
        rapport.append("=" * 100)
        rapport.append("")

        # Configuration
        rapport.append(f"📊 CONFIGURATION DE L'ANALYSE")
        rapport.append(f"   • Nombre de parties analysées : {self.nb_parties_analyse}")
        rapport.append(f"   • Nombre de mains valides : {len(self.sequences.get('index1', []))}")
        rapport.append(f"   • Dataset source : {self.dataset_path}")
        rapport.append("")

        # Synthèse
        if 'synthese' in self.resultats:
            synthese = self.resultats['synthese']
            rapport.append(f"🎯 VERDICT FINAL")
            rapport.append(f"   • {synthese['verdict_final']['verdict']}")
            rapport.append(f"   • Niveau de détection : {synthese['verdict_final']['niveau_detection']}")
            rapport.append(f"   • Score : {synthese['verdict_final']['score_detection']:.2f}")
            rapport.append(f"   • Confiance : {synthese['verdict_final']['confiance_globale']}")
            rapport.append("")

            # Phénomènes détectés
            rapport.append(f"🔬 PHÉNOMÈNES DÉTECTÉS")
            for nom, info in synthese['phenomenes_detectes'].items():
                statut = "✅ DÉTECTÉ" if info['detecte'] else "❌ NON DÉTECTÉ"
                rapport.append(f"   • {nom.replace('_', ' ').title()} : {statut}")
                for preuve in info['preuves']:
                    rapport.append(f"     - {preuve}")
            rapport.append("")

        # Détails techniques
        rapport.append(f"📈 RÉSULTATS TECHNIQUES DÉTAILLÉS")
        rapport.append("")

        for section, donnees in self.resultats.items():
            if section != 'synthese':
                rapport.append(f"   {section.replace('_', ' ').title()}")
                rapport.append(f"   {'-' * 50}")
                # Ajouter un résumé des résultats principaux
                if isinstance(donnees, dict):
                    for cle, valeur in list(donnees.items())[:5]:  # Limiter à 5 éléments
                        if isinstance(valeur, (int, float)):
                            rapport.append(f"     {cle} : {valeur}")
                        elif isinstance(valeur, dict) and 'p_value' in valeur:
                            rapport.append(f"     {cle} : p-value = {valeur['p_value']}")
                rapport.append("")

        contenu_rapport = "\n".join(rapport)

        # Sauvegarder si fichier spécifié
        if fichier_sortie:
            with open(fichier_sortie, 'w', encoding='utf-8') as f:
                f.write(contenu_rapport)
            print(f"📄 Rapport sauvegardé : {fichier_sortie}")

        return contenu_rapport

if __name__ == "__main__":
    # Générer 100000 parties complètes
    print("GENERATION DE 100000 PARTIES COMPLETES BACCARAT LUPASCO")
    print("FORMAT TXT ET JSON")
    print("=" * 60)

    # Générer le dataset complet
    filename_txt, filename_json = generer_dataset_complet()

    print(f"\n🎉 GENERATION TERMINEE AVEC SUCCES !")
    print(f"📝 Fichier TXT : {filename_txt}")
    print(f"📋 Fichier JSON : {filename_json}")
    print(f"💾 100000 parties complètes générées avec hasard cryptographiquement sécurisé")
    print(f"🔍 Les fichiers sont prêts pour l'analyse !")

    # EXEMPLE D'UTILISATION DE L'ANALYSEUR RÉVOLUTIONNAIRE
    print("\n" + "=" * 80)
    print("EXEMPLE D'UTILISATION DE L'ANALYSEUR RÉVOLUTIONNAIRE")
    print("=" * 80)
    print("# Pour analyser un dataset existant :")
    print("# analyseur = AnalyseurScientifiqueRevolutionnaire('dataset_baccarat_lupasco_20250701_092454.json', 100)")
    print("# if analyseur.charger_dataset():")
    print("#     resultats = analyseur.analyser_systeme_complexe_complet()")
    print("#     rapport = analyseur.generer_rapport_complet('rapport_analyse_revolutionnaire.txt')")
    print("#     print(rapport)")
    print("=" * 80)

    # DÉMONSTRATION AVEC LE DATASET EXISTANT (si disponible)
    dataset_path = "dataset_baccarat_lupasco_20250701_092454.json"
    try:
        import os
        if os.path.exists(dataset_path):
            print(f"\n🔬 DÉMONSTRATION DE L'ANALYSEUR RÉVOLUTIONNAIRE")
            print(f"📂 Dataset détecté : {dataset_path}")
            print(f"🎯 Lancement de l'analyse sur 100 parties (analyse complète)")

            # Créer l'analyseur
            analyseur = AnalyseurScientifiqueRevolutionnaire(dataset_path, 100)

            # Charger et analyser
            if analyseur.charger_dataset():
                print(f"\n🚀 ANALYSE RÉVOLUTIONNAIRE EN COURS...")
                resultats = analyseur.analyser_systeme_complexe_complet()

                print(f"\n📄 GÉNÉRATION DU RAPPORT...")
                rapport = analyseur.generer_rapport_complet()
                print(f"\n{rapport}")
            else:
                print(f"❌ Erreur lors du chargement du dataset")
        else:
            print(f"\n📝 Dataset {dataset_path} non trouvé")
            print(f"💡 Utilisez le code d'exemple ci-dessus pour analyser votre dataset")
    except Exception as e:
        print(f"\n⚠️ Erreur lors de la démonstration : {e}")
        print(f"💡 Utilisez le code d'exemple ci-dessus pour analyser votre dataset")
