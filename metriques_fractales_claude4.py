#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MÉTRIQUES FRACTALES CLAUDE 4 - BACCARAT LUPASCO
===============================================

Implémentation des 4 métriques fractales révolutionnaires proposées par Claude 4 :
1. Indice de Cohérence Fractale (ICF)
2. Coefficient de Persistance Temporelle (CPT)  
3. Entropie Hiérarchique Adaptative (EHA)
4. Indice de Chaos Contrôlé (ICC)

Basé sur l'analyse théorique des dossiers Fractale et Markov
et l'évidence fractale des ratios SYNC/DESYNC 49.7%/50.3%

Auteur : Système d'IA Augment
Date : 2025-07-03
"""

import json
import numpy as np
import pandas as pd
from scipy import stats
from scipy.spatial.distance import pdist
from collections import Counter, defaultdict
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

class MetriquesFractalesClaude4:
    """
    Implémentation des 4 métriques fractales révolutionnaires
    pour détecter la structure cachée du baccarat Lupasco
    """
    
    def __init__(self):
        self.data = None
        self.sequences = []
        self.encoder_index1 = LabelEncoder()
        self.encoder_index2 = LabelEncoder()
        self.encoder_index3 = LabelEncoder()
        self.encoder_index5 = LabelEncoder()
        
        # Résultats des métriques
        self.icf_results = {}
        self.cpt_results = {}
        self.eha_results = {}
        self.icc_results = {}
        
    def charger_donnees(self, fichier_json):
        """Charge les données du dataset baccarat condensé"""
        print("🔄 Chargement des données pour métriques Claude 4...")
        
        with open(fichier_json, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        print(f"✅ {len(self.data['parties_condensees'])} parties chargées")
        
        # Extraction des séquences
        for partie in self.data['parties_condensees']:
            mains = partie['mains_condensees']
            mains_valides = [m for m in mains if m['main_number'] != 1]
            
            if len(mains_valides) >= 20:  # Minimum pour analyse fractale
                sequence = {
                    'partie_id': partie['partie_number'],
                    'index1': [m['index1'] for m in mains_valides],
                    'index2': [m['index2'] for m in mains_valides],
                    'index3': [m['index3'] for m in mains_valides],
                    'index5': [m['index5'] for m in mains_valides]
                }
                self.sequences.append(sequence)
        
        print(f"✅ {len(self.sequences)} séquences valides extraites")
        
        # Encodage des indices
        self._encoder_indices()
        
    def _encoder_indices(self):
        """Encode tous les indices en valeurs numériques"""
        print("🔄 Encodage des indices...")
        
        all_index1, all_index2, all_index3, all_index5 = [], [], [], []
        
        for seq in self.sequences:
            all_index1.extend(seq['index1'])
            all_index2.extend(seq['index2'])
            all_index3.extend(seq['index3'])
            all_index5.extend(seq['index5'])
        
        self.encoder_index1.fit(all_index1)
        self.encoder_index2.fit(all_index2)
        self.encoder_index3.fit(all_index3)
        self.encoder_index5.fit(all_index5)
        
        for seq in self.sequences:
            seq['index1_encoded'] = self.encoder_index1.transform(seq['index1'])
            seq['index2_encoded'] = self.encoder_index2.transform(seq['index2'])
            seq['index3_encoded'] = self.encoder_index3.transform(seq['index3'])
            seq['index5_encoded'] = self.encoder_index5.transform(seq['index5'])
        
        print("✅ Encodage terminé")
        
    def calculer_icf_indice_coherence_fractale(self):
        """
        MÉTRIQUE 1 : Indice de Cohérence Fractale (ICF)
        
        Mesure la constance des ratios SYNC/DESYNC à travers les échelles.
        Basé sur l'évidence : 49.7%/50.3% constant sur 59M+ mains.
        
        Théorie : Si le système est fractal, les ratios doivent être 
        auto-similaires à différentes échelles temporelles.
        """
        print("🔄 Calcul ICF - Indice de Cohérence Fractale...")
        
        # Référence théorique : 49.7% SYNC, 50.3% DESYNC
        ratio_theorique_sync = 0.497
        ratio_theorique_desync = 0.503
        
        # Analyser à différentes échelles temporelles
        echelles = [10, 20, 30, 40, 50]  # Fenêtres glissantes
        coherences_par_echelle = {}
        
        for echelle in echelles:
            deviations = []
            
            for seq in self.sequences:
                if len(seq['index1']) < echelle:
                    continue
                    
                # Fenêtres glissantes de taille 'echelle'
                for i in range(len(seq['index1']) - echelle + 1):
                    fenetre_index1 = seq['index1'][i:i+echelle]
                    
                    # Calculer ratio SYNC/DESYNC dans cette fenêtre
                    count_sync = fenetre_index1.count(0)
                    count_desync = fenetre_index1.count(1)
                    
                    if count_sync + count_desync > 0:
                        ratio_sync = count_sync / (count_sync + count_desync)
                        ratio_desync = count_desync / (count_sync + count_desync)
                        
                        # Déviation par rapport au ratio théorique
                        deviation = abs(ratio_sync - ratio_theorique_sync)
                        deviations.append(deviation)
            
            # Cohérence = 1 - déviation moyenne
            coherence = 1.0 - np.mean(deviations) if deviations else 0.0
            coherences_par_echelle[echelle] = {
                'coherence': coherence,
                'deviation_std': np.std(deviations) if deviations else 0.0,
                'n_fenetres': len(deviations)
            }
            
            print(f"   Échelle {echelle:2d}: Cohérence = {coherence:.4f}")
        
        # ICF global = moyenne pondérée des cohérences
        icf_global = np.mean([c['coherence'] for c in coherences_par_echelle.values()])
        
        # Stabilité fractale = variance des cohérences (plus faible = plus fractal)
        stabilite_fractale = 1.0 - np.var([c['coherence'] for c in coherences_par_echelle.values()])
        
        self.icf_results = {
            'icf_global': icf_global,
            'stabilite_fractale': stabilite_fractale,
            'coherences_par_echelle': coherences_par_echelle,
            'interpretation': self._interpreter_icf(icf_global, stabilite_fractale)
        }
        
        print(f"✅ ICF Global: {icf_global:.4f}")
        print(f"✅ Stabilité Fractale: {stabilite_fractale:.4f}")
        
        return self.icf_results
    
    def _interpreter_icf(self, icf, stabilite):
        """Interprète les résultats ICF"""
        if icf > 0.95 and stabilite > 0.95:
            return "STRUCTURE FRACTALE PARFAITE - Ratios constants à toutes échelles"
        elif icf > 0.90 and stabilite > 0.90:
            return "STRUCTURE FRACTALE FORTE - Auto-similarité élevée"
        elif icf > 0.80:
            return "STRUCTURE FRACTALE MODÉRÉE - Patterns partiellement conservés"
        else:
            return "STRUCTURE NON-FRACTALE - Ratios variables selon l'échelle"
    
    def calculer_cpt_coefficient_persistance_temporelle(self):
        """
        MÉTRIQUE 2 : Coefficient de Persistance Temporelle (CPT)
        
        Analyse si les micro-patterns se répètent à différents horizons temporels.
        Utilise la dimension fractale pour quantifier la "mémoire" du système.
        
        Théorie : Un système déterministe montre une persistance des patterns
        à travers le temps (effet mémoire).
        """
        print("🔄 Calcul CPT - Coefficient de Persistance Temporelle...")
        
        # Analyser la persistance des patterns INDEX2 → INDEX3
        horizons = [1, 2, 3, 5, 8, 13]  # Séquence de Fibonacci
        persistances = {}
        
        for horizon in horizons:
            correlations_temporelles = []
            
            for seq in self.sequences:
                if len(seq['index2']) < horizon + 10:
                    continue
                
                # Créer des patterns de longueur 'horizon'
                patterns_debut = []
                patterns_fin = []
                
                for i in range(len(seq['index2']) - 2*horizon):
                    # Pattern au début
                    pattern_debut = tuple(seq['index2'][i:i+horizon])
                    # Pattern correspondant plus tard
                    pattern_fin = tuple(seq['index2'][i+horizon:i+2*horizon])
                    
                    patterns_debut.append(pattern_debut)
                    patterns_fin.append(pattern_fin)
                
                # Calculer corrélation entre patterns début/fin
                if patterns_debut and patterns_fin:
                    # Encoder les patterns comme entiers
                    patterns_debut_encoded = [hash(p) % 1000 for p in patterns_debut]
                    patterns_fin_encoded = [hash(p) % 1000 for p in patterns_fin]
                    
                    if len(set(patterns_debut_encoded)) > 1 and len(set(patterns_fin_encoded)) > 1:
                        correlation = np.corrcoef(patterns_debut_encoded, patterns_fin_encoded)[0,1]
                        if not np.isnan(correlation):
                            correlations_temporelles.append(abs(correlation))
            
            # Persistance = corrélation moyenne
            persistance = np.mean(correlations_temporelles) if correlations_temporelles else 0.0
            persistances[horizon] = {
                'persistance': persistance,
                'n_correlations': len(correlations_temporelles)
            }
            
            print(f"   Horizon {horizon:2d}: Persistance = {persistance:.4f}")
        
        # CPT = décroissance de la persistance (plus lente = plus de mémoire)
        horizons_list = list(persistances.keys())
        persistances_list = [persistances[h]['persistance'] for h in horizons_list]
        
        # Ajuster une décroissance exponentielle : y = a * exp(-b * x)
        if len(persistances_list) >= 3:
            try:
                # Régression log-linéaire
                log_persistances = [np.log(max(p, 1e-10)) for p in persistances_list]
                slope, intercept, r_value, _, _ = stats.linregress(horizons_list, log_persistances)
                
                # CPT = inverse du taux de décroissance (plus élevé = plus de mémoire)
                cpt_global = -1.0 / slope if slope < 0 else 0.0
                memoire_temporelle = r_value ** 2  # R² de l'ajustement
            except:
                cpt_global = np.mean(persistances_list)
                memoire_temporelle = 0.0
        else:
            cpt_global = np.mean(persistances_list)
            memoire_temporelle = 0.0
        
        self.cpt_results = {
            'cpt_global': cpt_global,
            'memoire_temporelle': memoire_temporelle,
            'persistances_par_horizon': persistances,
            'interpretation': self._interpreter_cpt(cpt_global, memoire_temporelle)
        }
        
        print(f"✅ CPT Global: {cpt_global:.4f}")
        print(f"✅ Mémoire Temporelle: {memoire_temporelle:.4f}")
        
        return self.cpt_results
    
    def _interpreter_cpt(self, cpt, memoire):
        """Interprète les résultats CPT"""
        if cpt > 10 and memoire > 0.8:
            return "MÉMOIRE TEMPORELLE FORTE - Patterns persistent longtemps"
        elif cpt > 5 and memoire > 0.6:
            return "MÉMOIRE TEMPORELLE MODÉRÉE - Persistance détectable"
        elif cpt > 2:
            return "MÉMOIRE TEMPORELLE FAIBLE - Persistance limitée"
        else:
            return "ABSENCE DE MÉMOIRE - Patterns non persistants"

    def calculer_eha_entropie_hierarchique_adaptative(self):
        """
        MÉTRIQUE 3 : Entropie Hiérarchique Adaptative (EHA)

        Mesure l'information contenue à chaque niveau de la hiérarchie.
        Détecte les anomalies par rapport à la distribution attendue.

        Basé sur la structure hiérarchique :
        NIVEAU 1: INDEX1 (SYNC/DESYNC)
        NIVEAU 2: INDEX3 (BANKER/PLAYER/TIE)
        NIVEAU 3: INDEX2 (A/B/C cartes)
        """
        print("🔄 Calcul EHA - Entropie Hiérarchique Adaptative...")

        # Collecter toutes les transitions hiérarchiques
        transitions_niveau1 = []  # INDEX1 seul
        transitions_niveau2 = []  # INDEX1 + INDEX3
        transitions_niveau3 = []  # INDEX1 + INDEX3 + INDEX2

        for seq in self.sequences:
            for i in range(len(seq['index1'])):
                # Niveau 1 : INDEX1 seulement
                transitions_niveau1.append(seq['index1'][i])

                # Niveau 2 : INDEX1 + INDEX3
                transitions_niveau2.append((seq['index1'][i], seq['index3'][i]))

                # Niveau 3 : INDEX1 + INDEX3 + INDEX2
                transitions_niveau3.append((seq['index1'][i], seq['index3'][i], seq['index2'][i]))

        # Calculer l'entropie à chaque niveau
        entropie_niveau1 = self._calculer_entropie_shannon(transitions_niveau1)
        entropie_niveau2 = self._calculer_entropie_shannon(transitions_niveau2)
        entropie_niveau3 = self._calculer_entropie_shannon(transitions_niveau3)

        # Entropies conditionnelles (information ajoutée par chaque niveau)
        # H(INDEX3 | INDEX1) = H(INDEX1, INDEX3) - H(INDEX1)
        info_ajoutee_niveau2 = entropie_niveau2 - entropie_niveau1

        # H(INDEX2 | INDEX1, INDEX3) = H(INDEX1, INDEX3, INDEX2) - H(INDEX1, INDEX3)
        info_ajoutee_niveau3 = entropie_niveau3 - entropie_niveau2

        # Entropies théoriques pour un système parfaitement équilibré
        # Niveau 1 : 2 états équiprobables → H = log2(2) = 1.0
        entropie_theorique_niveau1 = np.log2(2)

        # Niveau 2 : 2×3 = 6 états équiprobables → H = log2(6) ≈ 2.585
        entropie_theorique_niveau2 = np.log2(6)

        # Niveau 3 : 2×3×3 = 18 états équiprobables → H = log2(18) ≈ 4.170
        entropie_theorique_niveau3 = np.log2(18)

        # Efficacité informationnelle (entropie réelle / entropie théorique)
        efficacite_niveau1 = entropie_niveau1 / entropie_theorique_niveau1
        efficacite_niveau2 = entropie_niveau2 / entropie_theorique_niveau2
        efficacite_niveau3 = entropie_niveau3 / entropie_theorique_niveau3

        # Détection d'anomalies (écart par rapport à l'équilibre théorique)
        anomalie_niveau1 = abs(efficacite_niveau1 - 1.0)
        anomalie_niveau2 = abs(efficacite_niveau2 - 1.0)
        anomalie_niveau3 = abs(efficacite_niveau3 - 1.0)

        # EHA global = moyenne pondérée des efficacités
        eha_global = (efficacite_niveau1 + efficacite_niveau2 + efficacite_niveau3) / 3

        # Indice de structuration hiérarchique
        structuration = 1.0 - np.mean([anomalie_niveau1, anomalie_niveau2, anomalie_niveau3])

        self.eha_results = {
            'eha_global': eha_global,
            'structuration_hierarchique': structuration,
            'entropies': {
                'niveau1': entropie_niveau1,
                'niveau2': entropie_niveau2,
                'niveau3': entropie_niveau3
            },
            'entropies_theoriques': {
                'niveau1': entropie_theorique_niveau1,
                'niveau2': entropie_theorique_niveau2,
                'niveau3': entropie_theorique_niveau3
            },
            'efficacites': {
                'niveau1': efficacite_niveau1,
                'niveau2': efficacite_niveau2,
                'niveau3': efficacite_niveau3
            },
            'informations_ajoutees': {
                'niveau2': info_ajoutee_niveau2,
                'niveau3': info_ajoutee_niveau3
            },
            'anomalies': {
                'niveau1': anomalie_niveau1,
                'niveau2': anomalie_niveau2,
                'niveau3': anomalie_niveau3
            },
            'interpretation': self._interpreter_eha(eha_global, structuration)
        }

        print(f"✅ EHA Global: {eha_global:.4f}")
        print(f"✅ Structuration Hiérarchique: {structuration:.4f}")
        print(f"   Niveau 1 - Efficacité: {efficacite_niveau1:.4f}")
        print(f"   Niveau 2 - Efficacité: {efficacite_niveau2:.4f}")
        print(f"   Niveau 3 - Efficacité: {efficacite_niveau3:.4f}")

        return self.eha_results

    def _calculer_entropie_shannon(self, sequence):
        """Calcule l'entropie de Shannon d'une séquence"""
        if not sequence:
            return 0.0

        counts = Counter(sequence)
        total = len(sequence)

        entropy = 0.0
        for count in counts.values():
            prob = count / total
            if prob > 0:
                entropy -= prob * np.log2(prob)

        return entropy

    def _interpreter_eha(self, eha, structuration):
        """Interprète les résultats EHA"""
        if eha > 0.95 and structuration > 0.95:
            return "HIÉRARCHIE PARFAITE - Information optimalement distribuée"
        elif eha > 0.90 and structuration > 0.90:
            return "HIÉRARCHIE FORTE - Structure informationnelle claire"
        elif eha > 0.80:
            return "HIÉRARCHIE MODÉRÉE - Organisation partielle détectable"
        else:
            return "HIÉRARCHIE FAIBLE - Distribution informationnelle déséquilibrée"

    def calculer_icc_indice_chaos_controle(self):
        """
        MÉTRIQUE 4 : Indice de Chaos Contrôlé (ICC)

        Quantifie l'équilibre entre prédictibilité et chaos.
        Exploite la stabilité du système pour identifier les déviations.

        Théorie : Un système déterministe montre un "chaos contrôlé" :
        - Assez de variabilité pour paraître aléatoire
        - Assez de structure pour être prédictible
        """
        print("🔄 Calcul ICC - Indice de Chaos Contrôlé...")

        # Analyser la prédictibilité des transitions INDEX2 → INDEX3
        transitions_observees = defaultdict(list)

        for seq in self.sequences:
            for i in range(len(seq['index2']) - 1):
                etat_actuel = (seq['index1'][i], seq['index2'][i], seq['index3'][i])
                etat_suivant = seq['index3'][i + 1]
                transitions_observees[etat_actuel].append(etat_suivant)

        # Calculer la prédictibilité de chaque état
        predictibilites = []
        entropies_conditionnelles = []

        for etat, suivants in transitions_observees.items():
            if len(suivants) >= 5:  # Minimum pour statistiques fiables
                # Prédictibilité = probabilité de l'état le plus fréquent
                counts = Counter(suivants)
                max_count = max(counts.values())
                predictibilite = max_count / len(suivants)
                predictibilites.append(predictibilite)

                # Entropie conditionnelle de cet état
                entropie = self._calculer_entropie_shannon(suivants)
                entropies_conditionnelles.append(entropie)

        # Prédictibilité moyenne du système
        predictibilite_moyenne = np.mean(predictibilites) if predictibilites else 0.0

        # Chaos = entropie conditionnelle moyenne
        chaos_moyen = np.mean(entropies_conditionnelles) if entropies_conditionnelles else 0.0

        # Entropie maximale pour 3 états (BANKER/PLAYER/TIE)
        entropie_max = np.log2(3)

        # Normaliser le chaos (0 = déterministe, 1 = chaos total)
        chaos_normalise = chaos_moyen / entropie_max

        # ICC = équilibre optimal entre prédictibilité et chaos
        # Formule : ICC = 2 * predictibilite * (1 - chaos_normalise)
        # Maximum quand predictibilite élevée ET chaos modéré
        icc_global = 2 * predictibilite_moyenne * (1 - chaos_normalise)

        # Stabilité du chaos (variance des entropies conditionnelles)
        stabilite_chaos = 1.0 - (np.var(entropies_conditionnelles) / (entropie_max**2)) if entropies_conditionnelles else 0.0

        # Détection de déviations (états avec prédictibilité anormale)
        seuil_deviation = np.mean(predictibilites) + 2 * np.std(predictibilites) if predictibilites else 1.0
        etats_deviants = []

        for etat, suivants in transitions_observees.items():
            if len(suivants) >= 5:
                counts = Counter(suivants)
                max_count = max(counts.values())
                predictibilite = max_count / len(suivants)

                if predictibilite > seuil_deviation:
                    etats_deviants.append({
                        'etat': etat,
                        'predictibilite': predictibilite,
                        'n_observations': len(suivants),
                        'distribution': dict(counts)
                    })

        self.icc_results = {
            'icc_global': icc_global,
            'predictibilite_moyenne': predictibilite_moyenne,
            'chaos_normalise': chaos_normalise,
            'stabilite_chaos': stabilite_chaos,
            'n_etats_analyses': len(predictibilites),
            'etats_deviants': sorted(etats_deviants, key=lambda x: x['predictibilite'], reverse=True)[:10],
            'interpretation': self._interpreter_icc(icc_global, predictibilite_moyenne, chaos_normalise)
        }

        print(f"✅ ICC Global: {icc_global:.4f}")
        print(f"✅ Prédictibilité Moyenne: {predictibilite_moyenne:.4f}")
        print(f"✅ Chaos Normalisé: {chaos_normalise:.4f}")
        print(f"✅ États Déviants Détectés: {len(etats_deviants)}")

        return self.icc_results

    def _interpreter_icc(self, icc, predictibilite, chaos):
        """Interprète les résultats ICC"""
        if icc > 0.8 and 0.6 < predictibilite < 0.9 and 0.3 < chaos < 0.7:
            return "CHAOS CONTRÔLÉ OPTIMAL - Équilibre parfait prédictibilité/variabilité"
        elif icc > 0.6:
            return "CHAOS CONTRÔLÉ MODÉRÉ - Structure détectable avec variabilité"
        elif predictibilite > 0.8:
            return "SYSTÈME TROP PRÉDICTIBLE - Manque de variabilité naturelle"
        elif chaos > 0.8:
            return "CHAOS EXCESSIF - Système trop aléatoire"
        else:
            return "DÉSÉQUILIBRE CHAOS/ORDRE - Structure non optimale"
