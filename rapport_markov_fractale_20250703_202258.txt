RAPPORT COMPLET D'ANALYSE MARKOV-FRACTALE BACCARAT
============================================================

Date d'analyse : 2025-07-03 20:22:58
Parties analysées : 10,000

1. STATISTIQUES DE BASE
------------------------------
Total mains analysées : 610,000
INDEX2 - A: 227,286, B: 190,612, C: 182,102
INDEX3 - BANKER: 275,064, PLAYER: 267,687, TIE: 57,249

2. ANALYSE DES CHAÎNES DE MARKOV
-----------------------------------
Mat<PERSON> de transition INDEX2 → INDEX3:
            BANKER    PLAYER       TIE
       A     0.452     0.453     0.094
       B     0.411     0.485     0.104
       C     0.515     0.396     0.088

Propriétés markoviennes:
  stochastic: True
  sparsity: 0.0
  probabilites_stationnaires: {'A': np.float64(0.****************), 'B': np.float64(0.****************), 'C': np.float64(0.*****************)}
  markov_score: 1.0

3. ANALYSE FRACTALE
--------------------
dimension_moyenne: 0.****************
dimension_std: 0.025569368503074185
embedding_correlation: 0.*****************
embeddings_count: 80
correlations_lag:
  1: 0.125
  2: 0.082
  3: 0.117
  5: 0.104
  10: 0.100
patterns_count: 10

4. PATTERNS AUTO-SIMILAIRES DÉTECTÉS
----------------------------------------
  'AA' : 397 occurrences
  'BA' : 352 occurrences
  'AC' : 351 occurrences
  'AB' : 350 occurrences
  'CA' : 345 occurrences
  'BB' : 298 occurrences
  'CC' : 293 occurrences
  'CB' : 282 occurrences
  'BC' : 279 occurrences
  'AAA' : 133 occurrences

============================================================
Fin du rapport
